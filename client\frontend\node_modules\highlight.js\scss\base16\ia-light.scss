pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: iA Light
  Author: iA Inc. (modified by aramisgithub)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme ia-light
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #f6f6f6  Default Background
base01  #dedede  Lighter Background (Used for status bars, line number and folding marks)
base02  #bde5f2  Selection Background
base03  #898989  Comments, Invisibles, Line Highlighting
base04  #767676  Dark Foreground (Used for status bars)
base05  #181818  Default Foreground, Caret, Delimiters, Operators
base06  #e8e8e8  Light Foreground (Not often used)
base07  #f8f8f8  Light Background (Not often used)
base08  #9c5a02  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #c43e18  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #c48218  Classes, Markup Bold, Search Text Background
base0B  #38781c  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #2d6bb1  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #48bac2  Functions, Methods, Attribute IDs, Headings
base0E  #a94598  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #8b6c37  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #181818;
  background: #f6f6f6
}
.hljs::selection,
.hljs ::selection {
  background-color: #bde5f2;
  color: #181818
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #898989 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #898989
}
/* base04 - #767676 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #767676
}
/* base05 - #181818 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #181818
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #9c5a02
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #c43e18
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #c48218
}
.hljs-strong {
  font-weight: bold;
  color: #c48218
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #38781c
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #2d6bb1
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #48bac2
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #a94598
}
.hljs-emphasis {
  color: #a94598;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #8b6c37
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}
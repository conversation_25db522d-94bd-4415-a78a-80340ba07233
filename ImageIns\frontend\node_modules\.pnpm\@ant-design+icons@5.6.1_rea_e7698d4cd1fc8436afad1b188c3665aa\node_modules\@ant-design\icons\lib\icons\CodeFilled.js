"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _CodeFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/CodeFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var CodeFilled = function CodeFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _CodeFilled.default
  }));
};

/**![code](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNTEzLjEgNTE4LjFsLTE5MiAxNjFjLTUuMiA0LjQtMTMuMS43LTEzLjEtNi4xdi02Mi43YzAtMi4zIDEuMS00LjYgMi45LTYuMUw0MjAuNyA1MTJsLTEwOS44LTkyLjJhNy42MyA3LjYzIDAgMDEtMi45LTYuMVYzNTFjMC02LjggNy45LTEwLjUgMTMuMS02LjFsMTkyIDE2MC45YzMuOSAzLjIgMy45IDkuMSAwIDEyLjN6TTcxNiA2NzNjMCA0LjQtMy40IDgtNy41IDhoLTE4NWMtNC4xIDAtNy41LTMuNi03LjUtOHYtNDhjMC00LjQgMy40LTggNy41LThoMTg1YzQuMSAwIDcuNSAzLjYgNy41IDh2NDh6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(CodeFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CodeFilled';
}
var _default = exports.default = RefIcon;
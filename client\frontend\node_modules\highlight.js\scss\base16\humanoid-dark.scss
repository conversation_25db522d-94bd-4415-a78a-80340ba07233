pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Humanoid dark
  Author: <PERSON> (tasmo) Friese
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme humanoid-dark
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #232629  Default Background
base01  #333b3d  Lighter Background (Used for status bars, line number and folding marks)
base02  #484e54  Selection Background
base03  #60615d  Comments, Invisibles, Line Highlighting
base04  #c0c0bd  Dark Foreground (Used for status bars)
base05  #f8f8f2  Default Foreground, Caret, Delimiters, Operators
base06  #fcfcf6  Light Foreground (Not often used)
base07  #fcfcfc  Light Background (Not often used)
base08  #f11235  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #ff9505  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #ffb627  Classes, Markup Bold, Search Text Background
base0B  #02d849  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #0dd9d6  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #00a6fb  Functions, Methods, Attribute IDs, Headings
base0E  #f15ee3  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #b27701  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #f8f8f2;
  background: #232629
}
.hljs::selection,
.hljs ::selection {
  background-color: #484e54;
  color: #f8f8f2
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #60615d -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #60615d
}
/* base04 - #c0c0bd -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #c0c0bd
}
/* base05 - #f8f8f2 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #f8f8f2
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #f11235
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #ff9505
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #ffb627
}
.hljs-strong {
  font-weight: bold;
  color: #ffb627
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #02d849
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #0dd9d6
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #00a6fb
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #f15ee3
}
.hljs-emphasis {
  color: #f15ee3;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #b27701
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}
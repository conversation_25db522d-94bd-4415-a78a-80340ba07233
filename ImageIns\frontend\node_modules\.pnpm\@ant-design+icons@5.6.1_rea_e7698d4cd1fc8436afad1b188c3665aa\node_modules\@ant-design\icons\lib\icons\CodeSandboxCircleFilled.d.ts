import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![code-sandbox-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0yNDMuNyA1ODkuMkw1MTIgNzk0IDI2OC4zIDY1My4yVjM3MS44bDExMC02My42LS40LS4yaC4yTDUxMiAyMzFsMTM0IDc3aC0uMmwtLjMuMiAxMTAuMSA2My42djI4MS40ek0zMDcuOSA1MzYuN2w4Ny42IDQ5LjlWNjgxbDk2LjcgNTUuOVY1MjQuOEwzMDcuOSA0MTguNHptMjAzLjktMTUxLjhMNDE4IDMzMWwtOTEuMSA1Mi42IDE4NS4yIDEwNyAxODUuMi0xMDYuOS05MS40LTUyLjh6bTIwIDM1Mmw5Ny4zLTU2LjJ2LTk0LjFsODctNDkuNVY0MTguNUw1MzEuOCA1MjV6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;

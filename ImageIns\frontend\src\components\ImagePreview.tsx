'use client'

import React from 'react'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { FileImage } from 'lucide-react'

interface ImagePreviewProps {
  src: string | null
  alt?: string
  className?: string
}

export const ImagePreview: React.FC<ImagePreviewProps> = ({ 
  src, 
  alt = "Preview image", 
  className = "" 
}) => {
  if (!src) {
    return (
      <div className={`flex items-center justify-center text-neutral-500 dark:text-neutral-400 py-16 ${className}`}>
        <div className="text-center">
          <FileImage size={64} className="mx-auto mb-4 opacity-40" />
          <p className="text-lg font-medium">请选择一张图片进行上传</p>
          <p className="text-sm mt-2 opacity-75">支持 JPEG, PNG, GIF, WebP 格式</p>
        </div>
      </div>
    )
  }

  return (
    <motion.div 
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`relative ${className}`}
      style={{ height: '320px', width: '100%' }}
    >
      <Image
        src={src}
        alt={alt}
        fill
        className="object-contain rounded-lg"
        sizes="(max-width: 768px) 100vw, 50vw"
        priority
      />
    </motion.div>
  )
}

"use strict";
"use client";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.DisabledContextProvider = void 0;
var React = _interopRequireWildcard(require("react"));
const DisabledContext = /*#__PURE__*/React.createContext(false);
const DisabledContextProvider = ({
  children,
  disabled
}) => {
  const originDisabled = React.useContext(DisabledContext);
  return /*#__PURE__*/React.createElement(DisabledContext.Provider, {
    value: disabled !== null && disabled !== void 0 ? disabled : originDisabled
  }, children);
};
exports.DisabledContextProvider = DisabledContextProvider;
var _default = exports.default = DisabledContext;
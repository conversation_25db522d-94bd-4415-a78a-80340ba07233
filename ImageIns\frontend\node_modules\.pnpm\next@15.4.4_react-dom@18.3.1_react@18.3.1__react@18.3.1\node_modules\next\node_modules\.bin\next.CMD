@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\LLM\Learning\ai_coding\autocs\ImageIns\frontend\node_modules\.pnpm\next@15.4.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\node_modules\next\dist\bin\node_modules;D:\LLM\Learning\ai_coding\autocs\ImageIns\frontend\node_modules\.pnpm\next@15.4.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\node_modules\next\dist\node_modules;D:\LLM\Learning\ai_coding\autocs\ImageIns\frontend\node_modules\.pnpm\next@15.4.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\node_modules\next\node_modules;D:\LLM\Learning\ai_coding\autocs\ImageIns\frontend\node_modules\.pnpm\next@15.4.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\node_modules;D:\LLM\Learning\ai_coding\autocs\ImageIns\frontend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\LLM\Learning\ai_coding\autocs\ImageIns\frontend\node_modules\.pnpm\next@15.4.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\node_modules\next\dist\bin\node_modules;D:\LLM\Learning\ai_coding\autocs\ImageIns\frontend\node_modules\.pnpm\next@15.4.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\node_modules\next\dist\node_modules;D:\LLM\Learning\ai_coding\autocs\ImageIns\frontend\node_modules\.pnpm\next@15.4.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\node_modules\next\node_modules;D:\LLM\Learning\ai_coding\autocs\ImageIns\frontend\node_modules\.pnpm\next@15.4.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\node_modules;D:\LLM\Learning\ai_coding\autocs\ImageIns\frontend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\dist\bin\next" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\dist\bin\next" %*
)

pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Edge Dark
  Author: cjayross (https://github.com/cjayross)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme edge-dark
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #262729  Default Background
base01  #88909f  Lighter Background (Used for status bars, line number and folding marks)
base02  #b7bec9  Selection Background
base03  #3e4249  Comments, Invisibles, Line Highlighting
base04  #73b3e7  Dark Foreground (Used for status bars)
base05  #b7bec9  Default Foreground, Caret, Delimiters, Operators
base06  #d390e7  Light Foreground (Not often used)
base07  #3e4249  Light Background (Not often used)
base08  #e77171  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #e77171  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #dbb774  Classes, Markup Bold, Search Text Background
base0B  #a1bf78  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #5ebaa5  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #73b3e7  Functions, Methods, Attribute IDs, Headings
base0E  #d390e7  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #5ebaa5  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #b7bec9;
  background: #262729
}
.hljs::selection,
.hljs ::selection {
  background-color: #b7bec9;
  color: #b7bec9
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #3e4249 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #3e4249
}
/* base04 - #73b3e7 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #73b3e7
}
/* base05 - #b7bec9 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #b7bec9
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #e77171
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #e77171
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #dbb774
}
.hljs-strong {
  font-weight: bold;
  color: #dbb774
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #a1bf78
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #5ebaa5
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #73b3e7
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #d390e7
}
.hljs-emphasis {
  color: #d390e7;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #5ebaa5
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}
import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![close-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNODgwIDExMmMxNy43IDAgMzIgMTQuMyAzMiAzMnY3MzZjMCAxNy43LTE0LjMgMzItMzIgMzJIMTQ0Yy0xNy43IDAtMzItMTQuMy0zMi0zMlYxNDRjMC0xNy43IDE0LjMtMzIgMzItMzJ6bS00MCA3MkgxODR2NjU2aDY1NlYxODR6TTY0MC4wMSAzMzguODNjLjAzIDAgLjA1LjAxLjA5LjA2bDQ1LjAyIDQ1LjAxYS4yLjIgMCAwMS4wNS4wOS4xMi4xMiAwIDAxMCAuMDdjMCAuMDItLjAxLjA0LS4wNS4wOEw1NTcuMjUgNTEybDEyNy44NyAxMjcuODZhLjI3LjI3IDAgMDEuMDUuMDZ2LjAyYS4xMi4xMiAwIDAxMCAuMDdjMCAuMDMtLjAxLjA1LS4wNS4wOWwtNDUuMDIgNDUuMDJhLjIuMiAwIDAxLS4wOS4wNS4xMi4xMiAwIDAxLS4wNyAwYy0uMDIgMC0uMDQtLjAxLS4wOC0uMDVMNTEyIDU1Ny4yNSAzODQuMTQgNjg1LjEyYy0uMDQuMDQtLjA2LjA1LS4wOC4wNWEuMTIuMTIgMCAwMS0uMDcgMGMtLjAzIDAtLjA1LS4wMS0uMDktLjA1bC00NS4wMi00NS4wMmEuMi4yIDAgMDEtLjA1LS4wOS4xMi4xMiAwIDAxMC0uMDdjMC0uMDIuMDEtLjA0LjA2LS4wOEw0NjYuNzUgNTEyIDMzOC44OCAzODQuMTRhLjI3LjI3IDAgMDEtLjA1LS4wNmwtLjAxLS4wMmEuMTIuMTIgMCAwMTAtLjA3YzAtLjAzLjAxLS4wNS4wNS0uMDlsNDUuMDItNDUuMDJhLjIuMiAwIDAxLjA5LS4wNS4xMi4xMiAwIDAxLjA3IDBjLjAyIDAgLjA0LjAxLjA4LjA2TDUxMiA0NjYuNzVsMTI3Ljg2LTEyNy44NmMuMDQtLjA1LjA2LS4wNi4wOC0uMDZhLjEyLjEyIDAgMDEuMDcgMHoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;

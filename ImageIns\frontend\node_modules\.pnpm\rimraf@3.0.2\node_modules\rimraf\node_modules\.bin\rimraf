#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/LLM/Learning/ai_coding/autocs/ImageIns/frontend/node_modules/.pnpm/rimraf@3.0.2/node_modules/rimraf/node_modules:/mnt/d/LLM/Learning/ai_coding/autocs/ImageIns/frontend/node_modules/.pnpm/rimraf@3.0.2/node_modules:/mnt/d/LLM/Learning/ai_coding/autocs/ImageIns/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/LLM/Learning/ai_coding/autocs/ImageIns/frontend/node_modules/.pnpm/rimraf@3.0.2/node_modules/rimraf/node_modules:/mnt/d/LLM/Learning/ai_coding/autocs/ImageIns/frontend/node_modules/.pnpm/rimraf@3.0.2/node_modules:/mnt/d/LLM/Learning/ai_coding/autocs/ImageIns/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin.js" "$@"
else
  exec node  "$basedir/../../bin.js" "$@"
fi

pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Brush Trees Dark
  Author: <PERSON> <<EMAIL>>
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme brush-trees-dark
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #485867  Default Background
base01  #5A6D7A  Lighter Background (Used for status bars, line number and folding marks)
base02  #6D828E  Selection Background
base03  #8299A1  Comments, Invisibles, Line Highlighting
base04  #98AFB5  Dark Foreground (Used for status bars)
base05  #B0C5C8  Default Foreground, Caret, Delimiters, Operators
base06  #C9DBDC  Light Foreground (Not often used)
base07  #E3EFEF  Light Background (Not often used)
base08  #b38686  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #d8bba2  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #aab386  Classes, Markup Bold, Search Text Background
base0B  #87b386  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #86b3b3  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #868cb3  Functions, Methods, Attribute IDs, Headings
base0E  #b386b2  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #b39f9f  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #B0C5C8;
  background: #485867
}
.hljs::selection,
.hljs ::selection {
  background-color: #6D828E;
  color: #B0C5C8
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #8299A1 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #8299A1
}
/* base04 - #98AFB5 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #98AFB5
}
/* base05 - #B0C5C8 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #B0C5C8
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #b38686
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #d8bba2
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #aab386
}
.hljs-strong {
  font-weight: bold;
  color: #aab386
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #87b386
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #86b3b3
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #868cb3
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #b386b2
}
.hljs-emphasis {
  color: #b386b2;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #b39f9f
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}
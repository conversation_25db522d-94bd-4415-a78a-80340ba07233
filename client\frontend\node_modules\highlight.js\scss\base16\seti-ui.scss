pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Seti UI
  Author: 
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme seti-ui
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #151718  Default Background
base01  #282a2b  Lighter Background (Used for status bars, line number and folding marks)
base02  #3B758C  Selection Background
base03  #41535B  Comments, Invisibles, Line Highlighting
base04  #43a5d5  Dark Foreground (Used for status bars)
base05  #d6d6d6  Default Foreground, Caret, Delimiters, Operators
base06  #eeeeee  Light Foreground (Not often used)
base07  #ffffff  Light Background (Not often used)
base08  #Cd3f45  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #db7b55  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #e6cd69  Classes, Markup Bold, Search Text Background
base0B  #9fca56  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #55dbbe  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #55b5db  Functions, Methods, Attribute IDs, Headings
base0E  #a074c4  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #8a553f  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #d6d6d6;
  background: #151718
}
.hljs::selection,
.hljs ::selection {
  background-color: #3B758C;
  color: #d6d6d6
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #41535B -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #41535B
}
/* base04 - #43a5d5 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #43a5d5
}
/* base05 - #d6d6d6 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #d6d6d6
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #Cd3f45
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #db7b55
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #e6cd69
}
.hljs-strong {
  font-weight: bold;
  color: #e6cd69
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #9fca56
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #55dbbe
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #55b5db
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #a074c4
}
.hljs-emphasis {
  color: #a074c4;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #8a553f
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}
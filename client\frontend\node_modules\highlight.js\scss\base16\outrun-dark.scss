pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Outrun Dark
  Author: <PERSON> (http://github.com/hugode<PERSON><PERSON>/)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme outrun-dark
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #00002A  Default Background
base01  #20204A  Lighter Background (Used for status bars, line number and folding marks)
base02  #30305A  Selection Background
base03  #50507A  Comments, Invisibles, Line Highlighting
base04  #B0B0DA  Dark Foreground (Used for status bars)
base05  #D0D0FA  Default Foreground, Caret, Delimiters, Operators
base06  #E0E0FF  Light Foreground (Not often used)
base07  #F5F5FF  Light Background (Not often used)
base08  #FF4242  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #FC8D28  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #F3E877  Classes, Markup Bold, Search Text Background
base0B  #59F176  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #0EF0F0  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #66B0FF  Functions, Methods, Attribute IDs, Headings
base0E  #F10596  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #F003EF  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #D0D0FA;
  background: #00002A
}
.hljs::selection,
.hljs ::selection {
  background-color: #30305A;
  color: #D0D0FA
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #50507A -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #50507A
}
/* base04 - #B0B0DA -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #B0B0DA
}
/* base05 - #D0D0FA -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #D0D0FA
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #FF4242
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #FC8D28
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #F3E877
}
.hljs-strong {
  font-weight: bold;
  color: #F3E877
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #59F176
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #0EF0F0
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #66B0FF
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #F10596
}
.hljs-emphasis {
  color: #F10596;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #F003EF
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}
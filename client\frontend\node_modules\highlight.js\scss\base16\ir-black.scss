pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: IR Black
  Author: <PERSON><PERSON><PERSON> (http://timotheepoisot.fr)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme ir-black
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #000000  Default Background
base01  #242422  Lighter Background (Used for status bars, line number and folding marks)
base02  #484844  Selection Background
base03  #6c6c66  Comments, Invisibles, Line Highlighting
base04  #918f88  Dark Foreground (Used for status bars)
base05  #b5b3aa  Default Foreground, Caret, Delimiters, Operators
base06  #d9d7cc  Light Foreground (Not often used)
base07  #fdfbee  Light Background (Not often used)
base08  #ff6c60  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #e9c062  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #ffffb6  Classes, Markup Bold, Search Text Background
base0B  #a8ff60  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #c6c5fe  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #96cbfe  Functions, Methods, Attribute IDs, Headings
base0E  #ff73fd  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #b18a3d  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #b5b3aa;
  background: #000000
}
.hljs::selection,
.hljs ::selection {
  background-color: #484844;
  color: #b5b3aa
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #6c6c66 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #6c6c66
}
/* base04 - #918f88 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #918f88
}
/* base05 - #b5b3aa -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #b5b3aa
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #ff6c60
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #e9c062
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #ffffb6
}
.hljs-strong {
  font-weight: bold;
  color: #ffffb6
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #a8ff60
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #c6c5fe
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #96cbfe
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #ff73fd
}
.hljs-emphasis {
  color: #ff73fd;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #b18a3d
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}
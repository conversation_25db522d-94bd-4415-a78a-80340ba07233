'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Upload, FileImage, Loader2, MessageSquare, AlertCircle } from 'lucide-react'
import { But<PERSON>, Card, message, Spin } from 'antd'
import { motion, AnimatePresence } from 'framer-motion'

import { uploadImage, analyzeImage } from '@/lib/api'
import { isValidImageType, formatFileSize } from '@/lib/utils'
import { ImagePreview } from '@/components/ImagePreview'
import type { ImageUploadResponse, ImageAnalysisResponse } from '@/types'

export default function ConversationPage() {
  const [selectedImage, setSelectedImage] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [uploadedImageData, setUploadedImageData] = useState<ImageUploadResponse | null>(null)
  const [analysisResult, setAnalysisResult] = useState<ImageAnalysisResponse | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 清理预览URL
  useEffect(() => {
    return () => {
      if (imagePreview) {
        URL.revokeObjectURL(imagePreview)
      }
    }
  }, [imagePreview])

  // 处理文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // 验证文件类型
    if (!isValidImageType(file)) {
      message.error('请选择有效的图片文件 (JPEG, PNG, GIF, WebP)')
      return
    }

    // 验证文件大小 (10MB)
    if (file.size > 10 * 1024 * 1024) {
      message.error('图片文件大小不能超过 10MB')
      return
    }

    setSelectedImage(file)
    setError(null)
    setAnalysisResult(null)
    setUploadedImageData(null)

    // 清理之前的预览URL
    if (imagePreview) {
      URL.revokeObjectURL(imagePreview)
    }

    // 创建预览URL
    const previewUrl = URL.createObjectURL(file)
    setImagePreview(previewUrl)
  }

  // 上传图片
  const handleImageUpload = async () => {
    if (!selectedImage) {
      message.warning('请先选择一张图片')
      return
    }

    setIsUploading(true)
    setError(null)

    try {
      const response = await uploadImage(selectedImage)
      setUploadedImageData(response)
      message.success('图片上传成功！')
    } catch (error) {
      console.error('Upload error:', error)
      setError('图片上传失败，请重试')
      message.error('图片上传失败，请重试')
    } finally {
      setIsUploading(false)
    }
  }

  // 分析图片
  const handleImageAnalysis = async () => {
    if (!uploadedImageData) {
      message.warning('请先上传图片')
      return
    }

    setIsAnalyzing(true)
    setError(null)

    try {
      const response = await analyzeImage({
        image_url: uploadedImageData.url,
        prompt: "从多个角度描述该图像"
      })
      setAnalysisResult(response)
      message.success('图片分析完成！')
    } catch (error) {
      console.error('Analysis error:', error)
      setError('图片分析失败，请重试')
      message.error('图片分析失败，请重试')
    } finally {
      setIsAnalyzing(false)
    }
  }

  // 重置状态
  const handleReset = () => {
    setSelectedImage(null)
    setImagePreview(null)
    setUploadedImageData(null)
    setAnalysisResult(null)
    setError(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
    if (imagePreview) {
      URL.revokeObjectURL(imagePreview)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 via-white to-primary-50/30 dark:from-neutral-900 dark:via-neutral-800 dark:to-neutral-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-5xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent mb-4">
            Image Analyzer
          </h1>
          <p className="text-xl text-neutral-600 dark:text-neutral-300 max-w-2xl mx-auto">
            AI-powered image analysis platform - Upload, analyze, and discover insights from your images
          </p>
        </motion.div>

        <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Image Display and Upload */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-6"
          >
            {/* Image Display Area */}
            <Card className="p-6 shadow-gemini border-0 bg-white/80 backdrop-blur-sm dark:bg-neutral-800/80">
              <h2 className="text-xl font-semibold mb-6 flex items-center text-neutral-800 dark:text-neutral-200">
                <FileImage className="mr-3 text-primary-500" size={24} />
                图片显示区域
              </h2>

              <div className="border-2 border-dashed border-neutral-300 dark:border-neutral-600 rounded-xl p-4 text-center bg-neutral-50/50 dark:bg-neutral-700/50 transition-all duration-300 hover:border-primary-400 dark:hover:border-primary-500">
                <ImagePreview src={imagePreview} alt="Selected image" />
              </div>

              {selectedImage && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mt-6 p-4 bg-neutral-100 dark:bg-neutral-700 rounded-lg"
                >
                  <div className="text-sm text-neutral-600 dark:text-neutral-400 space-y-1">
                    <p><span className="font-medium">文件名:</span> {selectedImage.name}</p>
                    <p><span className="font-medium">文件大小:</span> {formatFileSize(selectedImage.size)}</p>
                  </div>
                </motion.div>
              )}
            </Card>

            {/* Upload Controls */}
            <Card className="p-6 shadow-gemini border-0 bg-white/80 backdrop-blur-sm dark:bg-neutral-800/80">
              <div className="space-y-4">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileSelect}
                  className="hidden"
                />

                <Button
                  type="primary"
                  icon={<Upload />}
                  onClick={() => fileInputRef.current?.click()}
                  className="w-full h-12 text-base font-medium bg-primary-500 hover:bg-primary-600 border-0 shadow-lg hover:shadow-xl transition-all duration-200"
                  size="large"
                >
                  选择图片
                </Button>

                <AnimatePresence>
                  {selectedImage && !uploadedImageData && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                    >
                      <Button
                        type="default"
                        loading={isUploading}
                        onClick={handleImageUpload}
                        className="w-full h-12 text-base font-medium bg-google-green hover:bg-green-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200"
                        size="large"
                      >
                        {isUploading ? '上传中...' : '上传图片'}
                      </Button>
                    </motion.div>
                  )}

                  {uploadedImageData && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                    >
                      <Button
                        type="default"
                        icon={<MessageSquare />}
                        loading={isAnalyzing}
                        onClick={handleImageAnalysis}
                        className="w-full h-12 text-base font-medium bg-secondary-500 hover:bg-secondary-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200"
                        size="large"
                      >
                        {isAnalyzing ? '分析中...' : '分析图像'}
                      </Button>
                    </motion.div>
                  )}

                  {(selectedImage || uploadedImageData || analysisResult) && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                    >
                      <Button
                        type="text"
                        onClick={handleReset}
                        className="w-full h-10 text-base font-medium text-neutral-600 hover:text-neutral-800 dark:text-neutral-400 dark:hover:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-700 transition-all duration-200"
                        size="large"
                      >
                        重新开始
                      </Button>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </Card>
          </motion.div>

          {/* Right Column - Analysis Results */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
            className="space-y-6"
          >
            {/* Response Text Display Area */}
            <Card className="p-6 shadow-gemini border-0 bg-white/80 backdrop-blur-sm dark:bg-neutral-800/80">
              <h2 className="text-xl font-semibold mb-6 flex items-center text-neutral-800 dark:text-neutral-200">
                <MessageSquare className="mr-3 text-secondary-500" size={24} />
                分析结果
              </h2>

              <div className="border border-neutral-200 dark:border-neutral-700 rounded-xl p-6 min-h-[300px] max-h-[500px] overflow-y-auto bg-neutral-50/50 dark:bg-neutral-700/50">
                {isAnalyzing ? (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="flex flex-col items-center justify-center h-48"
                  >
                    <Spin size="large" />
                    <span className="ml-3 text-neutral-600 dark:text-neutral-400 text-lg font-medium mt-4">正在分析图片...</span>
                    <p className="text-neutral-500 dark:text-neutral-500 text-sm mt-2">请稍候，AI正在处理您的图片</p>
                  </motion.div>
                ) : analysisResult ? (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="space-y-6"
                  >
                    <div>
                      <h3 className="font-semibold text-neutral-900 dark:text-white mb-3 text-lg">检测结果:</h3>
                      <div className="bg-white dark:bg-neutral-800 rounded-lg p-4 border border-neutral-200 dark:border-neutral-600">
                        <pre className="text-neutral-700 dark:text-neutral-300 leading-relaxed text-sm font-mono whitespace-pre-wrap overflow-x-auto">
                          {analysisResult.description}
                        </pre>
                      </div>
                    </div>

                    {analysisResult.tags && analysisResult.tags.length > 0 && (
                      <div>
                        <h3 className="font-semibold text-neutral-900 dark:text-white mb-3 text-lg">相关标签:</h3>
                        <div className="flex flex-wrap gap-2">
                          {analysisResult.tags.map((tag, index) => (
                            <motion.span
                              key={index}
                              initial={{ opacity: 0, scale: 0.8 }}
                              animate={{ opacity: 1, scale: 1 }}
                              transition={{ delay: index * 0.1 }}
                              className="px-4 py-2 bg-gradient-to-r from-primary-100 to-secondary-100 dark:from-primary-900/30 dark:to-secondary-900/30 text-primary-800 dark:text-primary-200 rounded-full text-sm font-medium border border-primary-200 dark:border-primary-700"
                            >
                              {tag}
                            </motion.span>
                          ))}
                        </div>
                      </div>
                    )}
                  </motion.div>
                ) : (
                  <div className="flex items-center justify-center h-48 text-neutral-500 dark:text-neutral-400">
                    <div className="text-center">
                      <MessageSquare size={48} className="mx-auto mb-4 opacity-40" />
                      <p className="text-lg font-medium">等待图片分析</p>
                      <p className="text-sm mt-2 opacity-75">上传图片并点击"描述图像"查看分析结果</p>
                    </div>
                  </div>
                )}
              </div>
            </Card>

            {/* Error Display */}
            <AnimatePresence>
              {error && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                >
                  <Card className="p-4 border-0 bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 shadow-lg">
                    <div className="flex items-center text-red-700 dark:text-red-400">
                      <AlertCircle size={20} className="mr-3 flex-shrink-0" />
                      <span className="font-medium">{error}</span>
                    </div>
                  </Card>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Status Information */}
            <AnimatePresence>
              {uploadedImageData && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                >
                  <Card className="p-4 border-0 bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 shadow-lg">
                    <div className="text-green-700 dark:text-green-400">
                      <p className="font-semibold flex items-center">
                        <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                        图片上传成功
                      </p>
                      <p className="text-sm mt-1 opacity-90">文件名: {uploadedImageData.filename}</p>
                    </div>
                  </Card>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </div>
    </div>
  )
}

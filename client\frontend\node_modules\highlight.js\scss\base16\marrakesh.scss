pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Marrakesh
  Author: <PERSON> (http://github.com/Alexx2/)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme marrakesh
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #201602  Default Background
base01  #302e00  Lighter Background (Used for status bars, line number and folding marks)
base02  #5f5b17  Selection Background
base03  #6c6823  Comments, Invisibles, Line Highlighting
base04  #86813b  Dark Foreground (Used for status bars)
base05  #948e48  Default Foreground, Caret, Delimiters, Operators
base06  #ccc37a  Light Foreground (Not often used)
base07  #faf0a5  Light Background (Not often used)
base08  #c35359  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #b36144  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #a88339  Classes, Markup Bold, Search Text Background
base0B  #18974e  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #75a738  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #477ca1  Functions, Methods, Attribute IDs, Headings
base0E  #8868b3  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #b3588e  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #948e48;
  background: #201602
}
.hljs::selection,
.hljs ::selection {
  background-color: #5f5b17;
  color: #948e48
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #6c6823 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #6c6823
}
/* base04 - #86813b -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #86813b
}
/* base05 - #948e48 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #948e48
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #c35359
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #b36144
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #a88339
}
.hljs-strong {
  font-weight: bold;
  color: #a88339
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #18974e
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #75a738
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #477ca1
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #8868b3
}
.hljs-emphasis {
  color: #8868b3;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #b3588e
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}
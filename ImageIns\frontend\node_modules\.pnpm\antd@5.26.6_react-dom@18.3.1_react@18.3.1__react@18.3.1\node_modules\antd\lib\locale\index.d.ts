import * as React from 'react';
import type { ValidateMessages } from 'rc-field-form/lib/interface';
import type { PickerLocale as DatePickerLocale } from '../date-picker/generatePicker';
import type { TransferLocale as TransferLocaleForEmpty } from '../empty';
import type { ModalLocale } from '../modal/locale';
import type { PaginationLocale } from '../pagination/Pagination';
import type { PopconfirmLocale } from '../popconfirm/PurePanel';
import type { TableLocale } from '../table/interface';
import type { TourLocale } from '../tour/interface';
import type { TransferLocale } from '../transfer';
import type { UploadLocale } from '../upload/interface';
export { default as useLocale } from './useLocale';
export declare const ANT_MARK = "internalMark";
export interface Locale {
    locale: string;
    Pagination?: PaginationLocale;
    DatePicker?: DatePickerLocale;
    TimePicker?: Record<string, any>;
    Calendar?: Record<string, any>;
    Table?: TableLocale;
    Modal?: ModalLocale;
    Tour?: TourLocale;
    Popconfirm?: PopconfirmLocale;
    Transfer?: TransferLocale;
    Select?: Record<string, any>;
    Upload?: UploadLocale;
    Empty?: TransferLocaleForEmpty;
    global?: {
        placeholder?: string;
        close?: string;
    };
    Icon?: Record<string, any>;
    Text?: {
        edit?: any;
        copy?: any;
        copied?: any;
        expand?: any;
        collapse?: any;
    };
    Form?: {
        optional?: string;
        defaultValidateMessages: ValidateMessages;
    };
    Image?: {
        preview: string;
    };
    QRCode?: {
        expired?: string;
        refresh?: string;
        scanned?: string;
    };
    ColorPicker?: {
        presetEmpty: string;
        transparent: string;
        singleColor: string;
        gradientColor: string;
    };
}
export interface LocaleProviderProps {
    locale: Locale;
    children?: React.ReactNode;
}
declare const LocaleProvider: React.FC<LocaleProviderProps>;
export default LocaleProvider;

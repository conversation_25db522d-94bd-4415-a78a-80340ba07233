pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Atelier Sulphurpool Light
  Author: <PERSON> (http://atelierbramdehaan.nl)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme atelier-sulphurpool-light
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #f5f7ff  Default Background
base01  #dfe2f1  Lighter Background (Used for status bars, line number and folding marks)
base02  #979db4  Selection Background
base03  #898ea4  Comments, Invisibles, Line Highlighting
base04  #6b7394  Dark Foreground (Used for status bars)
base05  #5e6687  Default Foreground, Caret, Delimiters, Operators
base06  #293256  Light Foreground (Not often used)
base07  #202746  Light Background (Not often used)
base08  #c94922  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #c76b29  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #c08b30  Classes, Markup Bold, Search Text Background
base0B  #ac9739  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #22a2c9  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #3d8fd1  Functions, Methods, Attribute IDs, Headings
base0E  #6679cc  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #9c637a  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #5e6687;
  background: #f5f7ff
}
.hljs::selection,
.hljs ::selection {
  background-color: #979db4;
  color: #5e6687
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #898ea4 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #898ea4
}
/* base04 - #6b7394 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #6b7394
}
/* base05 - #5e6687 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #5e6687
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #c94922
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #c76b29
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #c08b30
}
.hljs-strong {
  font-weight: bold;
  color: #c08b30
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #ac9739
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #22a2c9
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #3d8fd1
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #6679cc
}
.hljs-emphasis {
  color: #6679cc;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #9c637a
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Bright
  Author: <PERSON> (http://chriskempson.com)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme bright
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #000000  Default Background
base01  #303030  Lighter Background (Used for status bars, line number and folding marks)
base02  #505050  Selection Background
base03  #b0b0b0  Comments, Invisibles, Line Highlighting
base04  #d0d0d0  Dark Foreground (Used for status bars)
base05  #e0e0e0  Default Foreground, Caret, Delimiters, Operators
base06  #f5f5f5  Light Foreground (Not often used)
base07  #ffffff  Light Background (Not often used)
base08  #fb0120  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #fc6d24  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #fda331  Classes, Markup Bold, Search Text Background
base0B  #a1c659  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #76c7b7  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #6fb3d2  Functions, Methods, Attribute IDs, Headings
base0E  #d381c3  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #be643c  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #e0e0e0;
  background: #000000
}
.hljs::selection,
.hljs ::selection {
  background-color: #505050;
  color: #e0e0e0
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #b0b0b0 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #b0b0b0
}
/* base04 - #d0d0d0 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #d0d0d0
}
/* base05 - #e0e0e0 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #e0e0e0
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #fb0120
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #fc6d24
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #fda331
}
.hljs-strong {
  font-weight: bold;
  color: #fda331
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #a1c659
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #76c7b7
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #6fb3d2
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #d381c3
}
.hljs-emphasis {
  color: #d381c3;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #be643c
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}
// THIS FILE IS AUTO GENERATED
import type { IconType } from '../lib/index'
export declare const BiAbacus: IconType;
export declare const BiAccessibility: IconType;
export declare const BiAddToQueue: IconType;
export declare const BiAdjust: IconType;
export declare const BiAlarmAdd: IconType;
export declare const BiAlarmExclamation: IconType;
export declare const BiAlarmOff: IconType;
export declare const BiAlarmSnooze: IconType;
export declare const BiAlarm: IconType;
export declare const BiAlbum: IconType;
export declare const BiAlignJustify: IconType;
export declare const BiAlignLeft: IconType;
export declare const BiAlignMiddle: IconType;
export declare const BiAlignRight: IconType;
export declare const BiAnalyse: IconType;
export declare const BiAnchor: IconType;
export declare const BiAngry: IconType;
export declare const BiAperture: IconType;
export declare const BiArch: IconType;
export declare const BiArchiveIn: IconType;
export declare const BiArchiveOut: IconType;
export declare const BiArchive: IconType;
export declare const BiArea: IconType;
export declare const BiArrowBack: IconType;
export declare const BiArrowFromBottom: IconType;
export declare const BiArrowFromLeft: IconType;
export declare const BiArrowFromRight: IconType;
export declare const BiArrowFromTop: IconType;
export declare const BiArrowToBottom: IconType;
export declare const BiArrowToLeft: IconType;
export declare const BiArrowToRight: IconType;
export declare const BiArrowToTop: IconType;
export declare const BiAt: IconType;
export declare const BiAtom: IconType;
export declare const BiAward: IconType;
export declare const BiBadgeCheck: IconType;
export declare const BiBadge: IconType;
export declare const BiBaguette: IconType;
export declare const BiBall: IconType;
export declare const BiBandAid: IconType;
export declare const BiBarChartAlt2: IconType;
export declare const BiBarChartAlt: IconType;
export declare const BiBarChartSquare: IconType;
export declare const BiBarChart: IconType;
export declare const BiBarcodeReader: IconType;
export declare const BiBarcode: IconType;
export declare const BiBaseball: IconType;
export declare const BiBasket: IconType;
export declare const BiBasketball: IconType;
export declare const BiBath: IconType;
export declare const BiBattery: IconType;
export declare const BiBed: IconType;
export declare const BiBeenHere: IconType;
export declare const BiBeer: IconType;
export declare const BiBellMinus: IconType;
export declare const BiBellOff: IconType;
export declare const BiBellPlus: IconType;
export declare const BiBell: IconType;
export declare const BiBible: IconType;
export declare const BiBitcoin: IconType;
export declare const BiBlanket: IconType;
export declare const BiBlock: IconType;
export declare const BiBluetooth: IconType;
export declare const BiBody: IconType;
export declare const BiBold: IconType;
export declare const BiBoltCircle: IconType;
export declare const BiBomb: IconType;
export declare const BiBone: IconType;
export declare const BiBong: IconType;
export declare const BiBookAdd: IconType;
export declare const BiBookAlt: IconType;
export declare const BiBookBookmark: IconType;
export declare const BiBookContent: IconType;
export declare const BiBookHeart: IconType;
export declare const BiBookOpen: IconType;
export declare const BiBookReader: IconType;
export declare const BiBook: IconType;
export declare const BiBookmarkAltMinus: IconType;
export declare const BiBookmarkAltPlus: IconType;
export declare const BiBookmarkAlt: IconType;
export declare const BiBookmarkHeart: IconType;
export declare const BiBookmarkMinus: IconType;
export declare const BiBookmarkPlus: IconType;
export declare const BiBookmark: IconType;
export declare const BiBookmarks: IconType;
export declare const BiBorderAll: IconType;
export declare const BiBorderBottom: IconType;
export declare const BiBorderInner: IconType;
export declare const BiBorderLeft: IconType;
export declare const BiBorderNone: IconType;
export declare const BiBorderOuter: IconType;
export declare const BiBorderRadius: IconType;
export declare const BiBorderRight: IconType;
export declare const BiBorderTop: IconType;
export declare const BiBot: IconType;
export declare const BiBowlHot: IconType;
export declare const BiBowlRice: IconType;
export declare const BiBowlingBall: IconType;
export declare const BiBox: IconType;
export declare const BiBracket: IconType;
export declare const BiBraille: IconType;
export declare const BiBrain: IconType;
export declare const BiBriefcaseAlt2: IconType;
export declare const BiBriefcaseAlt: IconType;
export declare const BiBriefcase: IconType;
export declare const BiBrightnessHalf: IconType;
export declare const BiBrightness: IconType;
export declare const BiBroadcast: IconType;
export declare const BiBrushAlt: IconType;
export declare const BiBrush: IconType;
export declare const BiBugAlt: IconType;
export declare const BiBug: IconType;
export declare const BiBuildingHouse: IconType;
export declare const BiBuilding: IconType;
export declare const BiBuildings: IconType;
export declare const BiBulb: IconType;
export declare const BiBullseye: IconType;
export declare const BiBuoy: IconType;
export declare const BiBusSchool: IconType;
export declare const BiBus: IconType;
export declare const BiCabinet: IconType;
export declare const BiCableCar: IconType;
export declare const BiCake: IconType;
export declare const BiCalculator: IconType;
export declare const BiCalendarAlt: IconType;
export declare const BiCalendarCheck: IconType;
export declare const BiCalendarEdit: IconType;
export declare const BiCalendarEvent: IconType;
export declare const BiCalendarExclamation: IconType;
export declare const BiCalendarHeart: IconType;
export declare const BiCalendarMinus: IconType;
export declare const BiCalendarPlus: IconType;
export declare const BiCalendarStar: IconType;
export declare const BiCalendarWeek: IconType;
export declare const BiCalendarX: IconType;
export declare const BiCalendar: IconType;
export declare const BiCameraHome: IconType;
export declare const BiCameraMovie: IconType;
export declare const BiCameraOff: IconType;
export declare const BiCamera: IconType;
export declare const BiCandles: IconType;
export declare const BiCapsule: IconType;
export declare const BiCaptions: IconType;
export declare const BiCar: IconType;
export declare const BiCard: IconType;
export declare const BiCaretDownCircle: IconType;
export declare const BiCaretDownSquare: IconType;
export declare const BiCaretDown: IconType;
export declare const BiCaretLeftCircle: IconType;
export declare const BiCaretLeftSquare: IconType;
export declare const BiCaretLeft: IconType;
export declare const BiCaretRightCircle: IconType;
export declare const BiCaretRightSquare: IconType;
export declare const BiCaretRight: IconType;
export declare const BiCaretUpCircle: IconType;
export declare const BiCaretUpSquare: IconType;
export declare const BiCaretUp: IconType;
export declare const BiCarousel: IconType;
export declare const BiCartAdd: IconType;
export declare const BiCartAlt: IconType;
export declare const BiCartDownload: IconType;
export declare const BiCart: IconType;
export declare const BiCast: IconType;
export declare const BiCategoryAlt: IconType;
export declare const BiCategory: IconType;
export declare const BiCctv: IconType;
export declare const BiCertification: IconType;
export declare const BiChair: IconType;
export declare const BiChalkboard: IconType;
export declare const BiChart: IconType;
export declare const BiChat: IconType;
export declare const BiCheckCircle: IconType;
export declare const BiCheckDouble: IconType;
export declare const BiCheckShield: IconType;
export declare const BiCheckSquare: IconType;
export declare const BiCheck: IconType;
export declare const BiCheckboxChecked: IconType;
export declare const BiCheckboxMinus: IconType;
export declare const BiCheckboxSquare: IconType;
export declare const BiCheckbox: IconType;
export declare const BiCheese: IconType;
export declare const BiChevronDownCircle: IconType;
export declare const BiChevronDownSquare: IconType;
export declare const BiChevronDown: IconType;
export declare const BiChevronLeftCircle: IconType;
export declare const BiChevronLeftSquare: IconType;
export declare const BiChevronLeft: IconType;
export declare const BiChevronRightCircle: IconType;
export declare const BiChevronRightSquare: IconType;
export declare const BiChevronRight: IconType;
export declare const BiChevronUpCircle: IconType;
export declare const BiChevronUpSquare: IconType;
export declare const BiChevronUp: IconType;
export declare const BiChevronsDown: IconType;
export declare const BiChevronsLeft: IconType;
export declare const BiChevronsRight: IconType;
export declare const BiChevronsUp: IconType;
export declare const BiChild: IconType;
export declare const BiChip: IconType;
export declare const BiChurch: IconType;
export declare const BiCircleHalf: IconType;
export declare const BiCircleQuarter: IconType;
export declare const BiCircleThreeQuarter: IconType;
export declare const BiCircle: IconType;
export declare const BiClinic: IconType;
export declare const BiClipboard: IconType;
export declare const BiCloset: IconType;
export declare const BiCloudDownload: IconType;
export declare const BiCloudDrizzle: IconType;
export declare const BiCloudLightRain: IconType;
export declare const BiCloudLightning: IconType;
export declare const BiCloudRain: IconType;
export declare const BiCloudSnow: IconType;
export declare const BiCloudUpload: IconType;
export declare const BiCloud: IconType;
export declare const BiCodeAlt: IconType;
export declare const BiCodeBlock: IconType;
export declare const BiCodeCurly: IconType;
export declare const BiCode: IconType;
export declare const BiCoffeeTogo: IconType;
export declare const BiCoffee: IconType;
export declare const BiCog: IconType;
export declare const BiCoinStack: IconType;
export declare const BiCoin: IconType;
export declare const BiCollapseAlt: IconType;
export declare const BiCollapseHorizontal: IconType;
export declare const BiCollapseVertical: IconType;
export declare const BiCollapse: IconType;
export declare const BiCollection: IconType;
export declare const BiColorFill: IconType;
export declare const BiColor: IconType;
export declare const BiColumns: IconType;
export declare const BiCommand: IconType;
export declare const BiCommentAdd: IconType;
export declare const BiCommentCheck: IconType;
export declare const BiCommentDetail: IconType;
export declare const BiCommentDots: IconType;
export declare const BiCommentEdit: IconType;
export declare const BiCommentError: IconType;
export declare const BiCommentMinus: IconType;
export declare const BiCommentX: IconType;
export declare const BiComment: IconType;
export declare const BiCompass: IconType;
export declare const BiConfused: IconType;
export declare const BiConversation: IconType;
export declare const BiCookie: IconType;
export declare const BiCool: IconType;
export declare const BiCopyAlt: IconType;
export declare const BiCopy: IconType;
export declare const BiCopyright: IconType;
export declare const BiCreditCardAlt: IconType;
export declare const BiCreditCardFront: IconType;
export declare const BiCreditCard: IconType;
export declare const BiCricketBall: IconType;
export declare const BiCrop: IconType;
export declare const BiCross: IconType;
export declare const BiCrosshair: IconType;
export declare const BiCrown: IconType;
export declare const BiCubeAlt: IconType;
export declare const BiCube: IconType;
export declare const BiCuboid: IconType;
export declare const BiCurrentLocation: IconType;
export declare const BiCustomize: IconType;
export declare const BiCut: IconType;
export declare const BiCycling: IconType;
export declare const BiCylinder: IconType;
export declare const BiData: IconType;
export declare const BiDesktop: IconType;
export declare const BiDetail: IconType;
export declare const BiDevices: IconType;
export declare const BiDialpadAlt: IconType;
export declare const BiDialpad: IconType;
export declare const BiDiamond: IconType;
export declare const BiDice1: IconType;
export declare const BiDice2: IconType;
export declare const BiDice3: IconType;
export declare const BiDice4: IconType;
export declare const BiDice5: IconType;
export declare const BiDice6: IconType;
export declare const BiDirections: IconType;
export declare const BiDisc: IconType;
export declare const BiDish: IconType;
export declare const BiDislike: IconType;
export declare const BiDizzy: IconType;
export declare const BiDna: IconType;
export declare const BiDockBottom: IconType;
export declare const BiDockLeft: IconType;
export declare const BiDockRight: IconType;
export declare const BiDockTop: IconType;
export declare const BiDollarCircle: IconType;
export declare const BiDollar: IconType;
export declare const BiDonateBlood: IconType;
export declare const BiDonateHeart: IconType;
export declare const BiDoorOpen: IconType;
export declare const BiDotsHorizontalRounded: IconType;
export declare const BiDotsHorizontal: IconType;
export declare const BiDotsVerticalRounded: IconType;
export declare const BiDotsVertical: IconType;
export declare const BiDoughnutChart: IconType;
export declare const BiDownArrowAlt: IconType;
export declare const BiDownArrowCircle: IconType;
export declare const BiDownArrow: IconType;
export declare const BiDownload: IconType;
export declare const BiDownvote: IconType;
export declare const BiDrink: IconType;
export declare const BiDroplet: IconType;
export declare const BiDumbbell: IconType;
export declare const BiDuplicate: IconType;
export declare const BiEditAlt: IconType;
export declare const BiEdit: IconType;
export declare const BiEnvelopeOpen: IconType;
export declare const BiEnvelope: IconType;
export declare const BiEqualizer: IconType;
export declare const BiEraser: IconType;
export declare const BiErrorAlt: IconType;
export declare const BiErrorCircle: IconType;
export declare const BiError: IconType;
export declare const BiEuro: IconType;
export declare const BiExclude: IconType;
export declare const BiExitFullscreen: IconType;
export declare const BiExit: IconType;
export declare const BiExpandAlt: IconType;
export declare const BiExpandHorizontal: IconType;
export declare const BiExpandVertical: IconType;
export declare const BiExpand: IconType;
export declare const BiExport: IconType;
export declare const BiExtension: IconType;
export declare const BiFace: IconType;
export declare const BiFastForwardCircle: IconType;
export declare const BiFastForward: IconType;
export declare const BiFemaleSign: IconType;
export declare const BiFemale: IconType;
export declare const BiFileBlank: IconType;
export declare const BiFileFind: IconType;
export declare const BiFile: IconType;
export declare const BiFilm: IconType;
export declare const BiFilterAlt: IconType;
export declare const BiFilter: IconType;
export declare const BiFingerprint: IconType;
export declare const BiFirstAid: IconType;
export declare const BiFirstPage: IconType;
export declare const BiFlag: IconType;
export declare const BiFolderMinus: IconType;
export declare const BiFolderOpen: IconType;
export declare const BiFolderPlus: IconType;
export declare const BiFolder: IconType;
export declare const BiFontColor: IconType;
export declare const BiFontFamily: IconType;
export declare const BiFontSize: IconType;
export declare const BiFont: IconType;
export declare const BiFoodMenu: IconType;
export declare const BiFoodTag: IconType;
export declare const BiFootball: IconType;
export declare const BiFork: IconType;
export declare const BiFridge: IconType;
export declare const BiFullscreen: IconType;
export declare const BiGame: IconType;
export declare const BiGasPump: IconType;
export declare const BiGhost: IconType;
export declare const BiGift: IconType;
export declare const BiGitBranch: IconType;
export declare const BiGitCommit: IconType;
export declare const BiGitCompare: IconType;
export declare const BiGitMerge: IconType;
export declare const BiGitPullRequest: IconType;
export declare const BiGitRepoForked: IconType;
export declare const BiGlassesAlt: IconType;
export declare const BiGlasses: IconType;
export declare const BiGlobeAlt: IconType;
export declare const BiGlobe: IconType;
export declare const BiGridAlt: IconType;
export declare const BiGridHorizontal: IconType;
export declare const BiGridSmall: IconType;
export declare const BiGridVertical: IconType;
export declare const BiGrid: IconType;
export declare const BiGroup: IconType;
export declare const BiHandicap: IconType;
export declare const BiHappyAlt: IconType;
export declare const BiHappyBeaming: IconType;
export declare const BiHappyHeartEyes: IconType;
export declare const BiHappy: IconType;
export declare const BiHardHat: IconType;
export declare const BiHash: IconType;
export declare const BiHdd: IconType;
export declare const BiHeading: IconType;
export declare const BiHeadphone: IconType;
export declare const BiHealth: IconType;
export declare const BiHeartCircle: IconType;
export declare const BiHeartSquare: IconType;
export declare const BiHeart: IconType;
export declare const BiHelpCircle: IconType;
export declare const BiHide: IconType;
export declare const BiHighlight: IconType;
export declare const BiHistory: IconType;
export declare const BiHive: IconType;
export declare const BiHomeAlt2: IconType;
export declare const BiHomeAlt: IconType;
export declare const BiHomeCircle: IconType;
export declare const BiHomeHeart: IconType;
export declare const BiHomeSmile: IconType;
export declare const BiHome: IconType;
export declare const BiHorizontalCenter: IconType;
export declare const BiHorizontalLeft: IconType;
export declare const BiHorizontalRight: IconType;
export declare const BiHotel: IconType;
export declare const BiHourglass: IconType;
export declare const BiIdCard: IconType;
export declare const BiImageAdd: IconType;
export declare const BiImageAlt: IconType;
export declare const BiImage: IconType;
export declare const BiImages: IconType;
export declare const BiImport: IconType;
export declare const BiInfinite: IconType;
export declare const BiInfoCircle: IconType;
export declare const BiInfoSquare: IconType;
export declare const BiInjection: IconType;
export declare const BiIntersect: IconType;
export declare const BiItalic: IconType;
export declare const BiJoystickAlt: IconType;
export declare const BiJoystickButton: IconType;
export declare const BiJoystick: IconType;
export declare const BiKey: IconType;
export declare const BiKnife: IconType;
export declare const BiLabel: IconType;
export declare const BiLandscape: IconType;
export declare const BiLaptop: IconType;
export declare const BiLastPage: IconType;
export declare const BiLaugh: IconType;
export declare const BiLayerMinus: IconType;
export declare const BiLayerPlus: IconType;
export declare const BiLayer: IconType;
export declare const BiLayout: IconType;
export declare const BiLeaf: IconType;
export declare const BiLeftArrowAlt: IconType;
export declare const BiLeftArrowCircle: IconType;
export declare const BiLeftArrow: IconType;
export declare const BiLeftDownArrowCircle: IconType;
export declare const BiLeftIndent: IconType;
export declare const BiLeftTopArrowCircle: IconType;
export declare const BiLemon: IconType;
export declare const BiLibrary: IconType;
export declare const BiLike: IconType;
export declare const BiLineChartDown: IconType;
export declare const BiLineChart: IconType;
export declare const BiLinkAlt: IconType;
export declare const BiLinkExternal: IconType;
export declare const BiLink: IconType;
export declare const BiLira: IconType;
export declare const BiListCheck: IconType;
export declare const BiListMinus: IconType;
export declare const BiListOl: IconType;
export declare const BiListPlus: IconType;
export declare const BiListUl: IconType;
export declare const BiLoaderAlt: IconType;
export declare const BiLoaderCircle: IconType;
export declare const BiLoader: IconType;
export declare const BiLocationPlus: IconType;
export declare const BiLockAlt: IconType;
export declare const BiLockOpenAlt: IconType;
export declare const BiLockOpen: IconType;
export declare const BiLock: IconType;
export declare const BiLogInCircle: IconType;
export declare const BiLogIn: IconType;
export declare const BiLogOutCircle: IconType;
export declare const BiLogOut: IconType;
export declare const BiLowVision: IconType;
export declare const BiMagnet: IconType;
export declare const BiMailSend: IconType;
export declare const BiMaleFemale: IconType;
export declare const BiMaleSign: IconType;
export declare const BiMale: IconType;
export declare const BiMapAlt: IconType;
export declare const BiMapPin: IconType;
export declare const BiMap: IconType;
export declare const BiMask: IconType;
export declare const BiMath: IconType;
export declare const BiMedal: IconType;
export declare const BiMehAlt: IconType;
export declare const BiMehBlank: IconType;
export declare const BiMeh: IconType;
export declare const BiMemoryCard: IconType;
export declare const BiMenuAltLeft: IconType;
export declare const BiMenuAltRight: IconType;
export declare const BiMenu: IconType;
export declare const BiMerge: IconType;
export declare const BiMessageAdd: IconType;
export declare const BiMessageAltAdd: IconType;
export declare const BiMessageAltCheck: IconType;
export declare const BiMessageAltDetail: IconType;
export declare const BiMessageAltDots: IconType;
export declare const BiMessageAltEdit: IconType;
export declare const BiMessageAltError: IconType;
export declare const BiMessageAltMinus: IconType;
export declare const BiMessageAltX: IconType;
export declare const BiMessageAlt: IconType;
export declare const BiMessageCheck: IconType;
export declare const BiMessageDetail: IconType;
export declare const BiMessageDots: IconType;
export declare const BiMessageEdit: IconType;
export declare const BiMessageError: IconType;
export declare const BiMessageMinus: IconType;
export declare const BiMessageRoundedAdd: IconType;
export declare const BiMessageRoundedCheck: IconType;
export declare const BiMessageRoundedDetail: IconType;
export declare const BiMessageRoundedDots: IconType;
export declare const BiMessageRoundedEdit: IconType;
export declare const BiMessageRoundedError: IconType;
export declare const BiMessageRoundedMinus: IconType;
export declare const BiMessageRoundedX: IconType;
export declare const BiMessageRounded: IconType;
export declare const BiMessageSquareAdd: IconType;
export declare const BiMessageSquareCheck: IconType;
export declare const BiMessageSquareDetail: IconType;
export declare const BiMessageSquareDots: IconType;
export declare const BiMessageSquareEdit: IconType;
export declare const BiMessageSquareError: IconType;
export declare const BiMessageSquareMinus: IconType;
export declare const BiMessageSquareX: IconType;
export declare const BiMessageSquare: IconType;
export declare const BiMessageX: IconType;
export declare const BiMessage: IconType;
export declare const BiMeteor: IconType;
export declare const BiMicrochip: IconType;
export declare const BiMicrophoneOff: IconType;
export declare const BiMicrophone: IconType;
export declare const BiMinusBack: IconType;
export declare const BiMinusCircle: IconType;
export declare const BiMinusFront: IconType;
export declare const BiMinus: IconType;
export declare const BiMobileAlt: IconType;
export declare const BiMobileLandscape: IconType;
export declare const BiMobileVibration: IconType;
export declare const BiMobile: IconType;
export declare const BiMoneyWithdraw: IconType;
export declare const BiMoney: IconType;
export declare const BiMoon: IconType;
export declare const BiMouseAlt: IconType;
export declare const BiMouse: IconType;
export declare const BiMoveHorizontal: IconType;
export declare const BiMoveVertical: IconType;
export declare const BiMove: IconType;
export declare const BiMoviePlay: IconType;
export declare const BiMovie: IconType;
export declare const BiMusic: IconType;
export declare const BiNavigation: IconType;
export declare const BiNetworkChart: IconType;
export declare const BiNews: IconType;
export declare const BiNoEntry: IconType;
export declare const BiNoSignal: IconType;
export declare const BiNote: IconType;
export declare const BiNotepad: IconType;
export declare const BiNotificationOff: IconType;
export declare const BiNotification: IconType;
export declare const BiObjectsHorizontalCenter: IconType;
export declare const BiObjectsHorizontalLeft: IconType;
export declare const BiObjectsHorizontalRight: IconType;
export declare const BiObjectsVerticalBottom: IconType;
export declare const BiObjectsVerticalCenter: IconType;
export declare const BiObjectsVerticalTop: IconType;
export declare const BiOutline: IconType;
export declare const BiPackage: IconType;
export declare const BiPaintRoll: IconType;
export declare const BiPaint: IconType;
export declare const BiPalette: IconType;
export declare const BiPaperPlane: IconType;
export declare const BiPaperclip: IconType;
export declare const BiParagraph: IconType;
export declare const BiParty: IconType;
export declare const BiPaste: IconType;
export declare const BiPauseCircle: IconType;
export declare const BiPause: IconType;
export declare const BiPen: IconType;
export declare const BiPencil: IconType;
export declare const BiPhoneCall: IconType;
export declare const BiPhoneIncoming: IconType;
export declare const BiPhoneOff: IconType;
export declare const BiPhoneOutgoing: IconType;
export declare const BiPhone: IconType;
export declare const BiPhotoAlbum: IconType;
export declare const BiPieChartAlt2: IconType;
export declare const BiPieChartAlt: IconType;
export declare const BiPieChart: IconType;
export declare const BiPin: IconType;
export declare const BiPlanet: IconType;
export declare const BiPlayCircle: IconType;
export declare const BiPlay: IconType;
export declare const BiPlug: IconType;
export declare const BiPlusCircle: IconType;
export declare const BiPlusMedical: IconType;
export declare const BiPlus: IconType;
export declare const BiPodcast: IconType;
export declare const BiPointer: IconType;
export declare const BiPoll: IconType;
export declare const BiPolygon: IconType;
export declare const BiPopsicle: IconType;
export declare const BiPound: IconType;
export declare const BiPowerOff: IconType;
export declare const BiPrinter: IconType;
export declare const BiPulse: IconType;
export declare const BiPurchaseTagAlt: IconType;
export declare const BiPurchaseTag: IconType;
export declare const BiPyramid: IconType;
export declare const BiQrScan: IconType;
export declare const BiQr: IconType;
export declare const BiQuestionMark: IconType;
export declare const BiRadar: IconType;
export declare const BiRadioCircleMarked: IconType;
export declare const BiRadioCircle: IconType;
export declare const BiRadio: IconType;
export declare const BiReceipt: IconType;
export declare const BiRectangle: IconType;
export declare const BiRecycle: IconType;
export declare const BiRedo: IconType;
export declare const BiReflectHorizontal: IconType;
export declare const BiReflectVertical: IconType;
export declare const BiRefresh: IconType;
export declare const BiRegistered: IconType;
export declare const BiRename: IconType;
export declare const BiRepeat: IconType;
export declare const BiReplyAll: IconType;
export declare const BiReply: IconType;
export declare const BiRepost: IconType;
export declare const BiReset: IconType;
export declare const BiRestaurant: IconType;
export declare const BiRevision: IconType;
export declare const BiRewindCircle: IconType;
export declare const BiRewind: IconType;
export declare const BiRfid: IconType;
export declare const BiRightArrowAlt: IconType;
export declare const BiRightArrowCircle: IconType;
export declare const BiRightArrow: IconType;
export declare const BiRightDownArrowCircle: IconType;
export declare const BiRightIndent: IconType;
export declare const BiRightTopArrowCircle: IconType;
export declare const BiRocket: IconType;
export declare const BiRotateLeft: IconType;
export declare const BiRotateRight: IconType;
export declare const BiRss: IconType;
export declare const BiRuble: IconType;
export declare const BiRuler: IconType;
export declare const BiRun: IconType;
export declare const BiRupee: IconType;
export declare const BiSad: IconType;
export declare const BiSave: IconType;
export declare const BiScan: IconType;
export declare const BiScatterChart: IconType;
export declare const BiScreenshot: IconType;
export declare const BiSearchAlt2: IconType;
export declare const BiSearchAlt: IconType;
export declare const BiSearch: IconType;
export declare const BiSelectMultiple: IconType;
export declare const BiSelection: IconType;
export declare const BiSend: IconType;
export declare const BiServer: IconType;
export declare const BiShapeCircle: IconType;
export declare const BiShapePolygon: IconType;
export declare const BiShapeSquare: IconType;
export declare const BiShapeTriangle: IconType;
export declare const BiShareAlt: IconType;
export declare const BiShare: IconType;
export declare const BiShekel: IconType;
export declare const BiShieldAlt2: IconType;
export declare const BiShieldAlt: IconType;
export declare const BiShieldMinus: IconType;
export declare const BiShieldPlus: IconType;
export declare const BiShieldQuarter: IconType;
export declare const BiShieldX: IconType;
export declare const BiShield: IconType;
export declare const BiShocked: IconType;
export declare const BiShoppingBag: IconType;
export declare const BiShowAlt: IconType;
export declare const BiShow: IconType;
export declare const BiShower: IconType;
export declare const BiShuffle: IconType;
export declare const BiSidebar: IconType;
export declare const BiSignal1: IconType;
export declare const BiSignal2: IconType;
export declare const BiSignal3: IconType;
export declare const BiSignal4: IconType;
export declare const BiSignal5: IconType;
export declare const BiSitemap: IconType;
export declare const BiSkipNextCircle: IconType;
export declare const BiSkipNext: IconType;
export declare const BiSkipPreviousCircle: IconType;
export declare const BiSkipPrevious: IconType;
export declare const BiSleepy: IconType;
export declare const BiSliderAlt: IconType;
export declare const BiSlider: IconType;
export declare const BiSlideshow: IconType;
export declare const BiSmile: IconType;
export declare const BiSortAZ: IconType;
export declare const BiSortAlt2: IconType;
export declare const BiSortDown: IconType;
export declare const BiSortUp: IconType;
export declare const BiSortZA: IconType;
export declare const BiSort: IconType;
export declare const BiSpa: IconType;
export declare const BiSpaceBar: IconType;
export declare const BiSpeaker: IconType;
export declare const BiSprayCan: IconType;
export declare const BiSpreadsheet: IconType;
export declare const BiSquareRounded: IconType;
export declare const BiSquare: IconType;
export declare const BiStar: IconType;
export declare const BiStation: IconType;
export declare const BiStats: IconType;
export declare const BiSticker: IconType;
export declare const BiStopCircle: IconType;
export declare const BiStop: IconType;
export declare const BiStopwatch: IconType;
export declare const BiStoreAlt: IconType;
export declare const BiStore: IconType;
export declare const BiStreetView: IconType;
export declare const BiStrikethrough: IconType;
export declare const BiSubdirectoryLeft: IconType;
export declare const BiSubdirectoryRight: IconType;
export declare const BiSun: IconType;
export declare const BiSupport: IconType;
export declare const BiSushi: IconType;
export declare const BiSwim: IconType;
export declare const BiSync: IconType;
export declare const BiTab: IconType;
export declare const BiTable: IconType;
export declare const BiTachometer: IconType;
export declare const BiTagAlt: IconType;
export declare const BiTag: IconType;
export declare const BiTargetLock: IconType;
export declare const BiTaskX: IconType;
export declare const BiTask: IconType;
export declare const BiTaxi: IconType;
export declare const BiTennisBall: IconType;
export declare const BiTerminal: IconType;
export declare const BiTestTube: IconType;
export declare const BiText: IconType;
export declare const BiTimeFive: IconType;
export declare const BiTime: IconType;
export declare const BiTimer: IconType;
export declare const BiTired: IconType;
export declare const BiToggleLeft: IconType;
export declare const BiToggleRight: IconType;
export declare const BiTone: IconType;
export declare const BiTrafficCone: IconType;
export declare const BiTrain: IconType;
export declare const BiTransferAlt: IconType;
export declare const BiTransfer: IconType;
export declare const BiTrashAlt: IconType;
export declare const BiTrash: IconType;
export declare const BiTrendingDown: IconType;
export declare const BiTrendingUp: IconType;
export declare const BiTrim: IconType;
export declare const BiTrip: IconType;
export declare const BiTrophy: IconType;
export declare const BiTv: IconType;
export declare const BiUnderline: IconType;
export declare const BiUndo: IconType;
export declare const BiUnite: IconType;
export declare const BiUniversalAccess: IconType;
export declare const BiUnlink: IconType;
export declare const BiUpArrowAlt: IconType;
export declare const BiUpArrowCircle: IconType;
export declare const BiUpArrow: IconType;
export declare const BiUpload: IconType;
export declare const BiUpsideDown: IconType;
export declare const BiUpvote: IconType;
export declare const BiUsb: IconType;
export declare const BiUserCheck: IconType;
export declare const BiUserCircle: IconType;
export declare const BiUserMinus: IconType;
export declare const BiUserPin: IconType;
export declare const BiUserPlus: IconType;
export declare const BiUserVoice: IconType;
export declare const BiUserX: IconType;
export declare const BiUser: IconType;
export declare const BiVector: IconType;
export declare const BiVerticalBottom: IconType;
export declare const BiVerticalCenter: IconType;
export declare const BiVerticalTop: IconType;
export declare const BiVial: IconType;
export declare const BiVideoOff: IconType;
export declare const BiVideoPlus: IconType;
export declare const BiVideoRecording: IconType;
export declare const BiVideo: IconType;
export declare const BiVoicemail: IconType;
export declare const BiVolumeFull: IconType;
export declare const BiVolumeLow: IconType;
export declare const BiVolumeMute: IconType;
export declare const BiVolume: IconType;
export declare const BiWalk: IconType;
export declare const BiWalletAlt: IconType;
export declare const BiWallet: IconType;
export declare const BiWater: IconType;
export declare const BiWebcam: IconType;
export declare const BiWifi0: IconType;
export declare const BiWifi1: IconType;
export declare const BiWifi2: IconType;
export declare const BiWifiOff: IconType;
export declare const BiWifi: IconType;
export declare const BiWind: IconType;
export declare const BiWindowAlt: IconType;
export declare const BiWindowClose: IconType;
export declare const BiWindowOpen: IconType;
export declare const BiWindow: IconType;
export declare const BiWindows: IconType;
export declare const BiWine: IconType;
export declare const BiWinkSmile: IconType;
export declare const BiWinkTongue: IconType;
export declare const BiWon: IconType;
export declare const BiWorld: IconType;
export declare const BiWrench: IconType;
export declare const BiXCircle: IconType;
export declare const BiX: IconType;
export declare const BiYen: IconType;
export declare const BiZoomIn: IconType;
export declare const BiZoomOut: IconType;
export declare const BiSolidAddToQueue: IconType;
export declare const BiSolidAdjustAlt: IconType;
export declare const BiSolidAdjust: IconType;
export declare const BiSolidAlarmAdd: IconType;
export declare const BiSolidAlarmExclamation: IconType;
export declare const BiSolidAlarmOff: IconType;
export declare const BiSolidAlarmSnooze: IconType;
export declare const BiSolidAlarm: IconType;
export declare const BiSolidAlbum: IconType;
export declare const BiSolidAmbulance: IconType;
export declare const BiSolidAnalyse: IconType;
export declare const BiSolidAngry: IconType;
export declare const BiSolidArch: IconType;
export declare const BiSolidArchiveIn: IconType;
export declare const BiSolidArchiveOut: IconType;
export declare const BiSolidArchive: IconType;
export declare const BiSolidArea: IconType;
export declare const BiSolidArrowFromBottom: IconType;
export declare const BiSolidArrowFromLeft: IconType;
export declare const BiSolidArrowFromRight: IconType;
export declare const BiSolidArrowFromTop: IconType;
export declare const BiSolidArrowToBottom: IconType;
export declare const BiSolidArrowToLeft: IconType;
export declare const BiSolidArrowToRight: IconType;
export declare const BiSolidArrowToTop: IconType;
export declare const BiSolidAward: IconType;
export declare const BiSolidBabyCarriage: IconType;
export declare const BiSolidBackpack: IconType;
export declare const BiSolidBadgeCheck: IconType;
export declare const BiSolidBadgeDollar: IconType;
export declare const BiSolidBadge: IconType;
export declare const BiSolidBaguette: IconType;
export declare const BiSolidBall: IconType;
export declare const BiSolidBalloon: IconType;
export declare const BiSolidBandAid: IconType;
export declare const BiSolidBank: IconType;
export declare const BiSolidBarChartAlt2: IconType;
export declare const BiSolidBarChartSquare: IconType;
export declare const BiSolidBarcode: IconType;
export declare const BiSolidBaseball: IconType;
export declare const BiSolidBasket: IconType;
export declare const BiSolidBasketball: IconType;
export declare const BiSolidBath: IconType;
export declare const BiSolidBatteryCharging: IconType;
export declare const BiSolidBatteryFull: IconType;
export declare const BiSolidBatteryLow: IconType;
export declare const BiSolidBattery: IconType;
export declare const BiSolidBed: IconType;
export declare const BiSolidBeenHere: IconType;
export declare const BiSolidBeer: IconType;
export declare const BiSolidBellMinus: IconType;
export declare const BiSolidBellOff: IconType;
export declare const BiSolidBellPlus: IconType;
export declare const BiSolidBellRing: IconType;
export declare const BiSolidBell: IconType;
export declare const BiSolidBible: IconType;
export declare const BiSolidBinoculars: IconType;
export declare const BiSolidBlanket: IconType;
export declare const BiSolidBoltCircle: IconType;
export declare const BiSolidBolt: IconType;
export declare const BiSolidBomb: IconType;
export declare const BiSolidBone: IconType;
export declare const BiSolidBong: IconType;
export declare const BiSolidBookAdd: IconType;
export declare const BiSolidBookAlt: IconType;
export declare const BiSolidBookBookmark: IconType;
export declare const BiSolidBookContent: IconType;
export declare const BiSolidBookHeart: IconType;
export declare const BiSolidBookOpen: IconType;
export declare const BiSolidBookReader: IconType;
export declare const BiSolidBook: IconType;
export declare const BiSolidBookmarkAltMinus: IconType;
export declare const BiSolidBookmarkAltPlus: IconType;
export declare const BiSolidBookmarkAlt: IconType;
export declare const BiSolidBookmarkHeart: IconType;
export declare const BiSolidBookmarkMinus: IconType;
export declare const BiSolidBookmarkPlus: IconType;
export declare const BiSolidBookmarkStar: IconType;
export declare const BiSolidBookmark: IconType;
export declare const BiSolidBookmarks: IconType;
export declare const BiSolidBot: IconType;
export declare const BiSolidBowlHot: IconType;
export declare const BiSolidBowlRice: IconType;
export declare const BiSolidBowlingBall: IconType;
export declare const BiSolidBox: IconType;
export declare const BiSolidBrain: IconType;
export declare const BiSolidBriefcaseAlt2: IconType;
export declare const BiSolidBriefcaseAlt: IconType;
export declare const BiSolidBriefcase: IconType;
export declare const BiSolidBrightnessHalf: IconType;
export declare const BiSolidBrightness: IconType;
export declare const BiSolidBrushAlt: IconType;
export declare const BiSolidBrush: IconType;
export declare const BiSolidBugAlt: IconType;
export declare const BiSolidBug: IconType;
export declare const BiSolidBuildingHouse: IconType;
export declare const BiSolidBuilding: IconType;
export declare const BiSolidBuildings: IconType;
export declare const BiSolidBulb: IconType;
export declare const BiSolidBullseye: IconType;
export declare const BiSolidBuoy: IconType;
export declare const BiSolidBusSchool: IconType;
export declare const BiSolidBus: IconType;
export declare const BiSolidBusiness: IconType;
export declare const BiSolidCabinet: IconType;
export declare const BiSolidCableCar: IconType;
export declare const BiSolidCake: IconType;
export declare const BiSolidCalculator: IconType;
export declare const BiSolidCalendarAlt: IconType;
export declare const BiSolidCalendarCheck: IconType;
export declare const BiSolidCalendarEdit: IconType;
export declare const BiSolidCalendarEvent: IconType;
export declare const BiSolidCalendarExclamation: IconType;
export declare const BiSolidCalendarHeart: IconType;
export declare const BiSolidCalendarMinus: IconType;
export declare const BiSolidCalendarPlus: IconType;
export declare const BiSolidCalendarStar: IconType;
export declare const BiSolidCalendarWeek: IconType;
export declare const BiSolidCalendarX: IconType;
export declare const BiSolidCalendar: IconType;
export declare const BiSolidCameraHome: IconType;
export declare const BiSolidCameraMovie: IconType;
export declare const BiSolidCameraOff: IconType;
export declare const BiSolidCameraPlus: IconType;
export declare const BiSolidCamera: IconType;
export declare const BiSolidCapsule: IconType;
export declare const BiSolidCaptions: IconType;
export declare const BiSolidCarBattery: IconType;
export declare const BiSolidCarCrash: IconType;
export declare const BiSolidCarGarage: IconType;
export declare const BiSolidCarMechanic: IconType;
export declare const BiSolidCarWash: IconType;
export declare const BiSolidCar: IconType;
export declare const BiSolidCard: IconType;
export declare const BiSolidCaretDownCircle: IconType;
export declare const BiSolidCaretDownSquare: IconType;
export declare const BiSolidCaretLeftCircle: IconType;
export declare const BiSolidCaretLeftSquare: IconType;
export declare const BiSolidCaretRightCircle: IconType;
export declare const BiSolidCaretRightSquare: IconType;
export declare const BiSolidCaretUpCircle: IconType;
export declare const BiSolidCaretUpSquare: IconType;
export declare const BiSolidCarousel: IconType;
export declare const BiSolidCartAdd: IconType;
export declare const BiSolidCartAlt: IconType;
export declare const BiSolidCartDownload: IconType;
export declare const BiSolidCart: IconType;
export declare const BiSolidCastle: IconType;
export declare const BiSolidCat: IconType;
export declare const BiSolidCategoryAlt: IconType;
export declare const BiSolidCategory: IconType;
export declare const BiSolidCctv: IconType;
export declare const BiSolidCertification: IconType;
export declare const BiSolidChalkboard: IconType;
export declare const BiSolidChart: IconType;
export declare const BiSolidChat: IconType;
export declare const BiSolidCheckCircle: IconType;
export declare const BiSolidCheckShield: IconType;
export declare const BiSolidCheckSquare: IconType;
export declare const BiSolidCheckboxChecked: IconType;
export declare const BiSolidCheckboxMinus: IconType;
export declare const BiSolidCheckbox: IconType;
export declare const BiSolidCheese: IconType;
export declare const BiSolidChess: IconType;
export declare const BiSolidChevronDownCircle: IconType;
export declare const BiSolidChevronDownSquare: IconType;
export declare const BiSolidChevronDown: IconType;
export declare const BiSolidChevronLeftCircle: IconType;
export declare const BiSolidChevronLeftSquare: IconType;
export declare const BiSolidChevronLeft: IconType;
export declare const BiSolidChevronRightCircle: IconType;
export declare const BiSolidChevronRightSquare: IconType;
export declare const BiSolidChevronRight: IconType;
export declare const BiSolidChevronUpCircle: IconType;
export declare const BiSolidChevronUpSquare: IconType;
export declare const BiSolidChevronUp: IconType;
export declare const BiSolidChevronsDown: IconType;
export declare const BiSolidChevronsLeft: IconType;
export declare const BiSolidChevronsRight: IconType;
export declare const BiSolidChevronsUp: IconType;
export declare const BiSolidChip: IconType;
export declare const BiSolidChurch: IconType;
export declare const BiSolidCircleHalf: IconType;
export declare const BiSolidCircleQuarter: IconType;
export declare const BiSolidCircleThreeQuarter: IconType;
export declare const BiSolidCircle: IconType;
export declare const BiSolidCity: IconType;
export declare const BiSolidClinic: IconType;
export declare const BiSolidCloudDownload: IconType;
export declare const BiSolidCloudLightning: IconType;
export declare const BiSolidCloudRain: IconType;
export declare const BiSolidCloudUpload: IconType;
export declare const BiSolidCloud: IconType;
export declare const BiSolidCoffeeAlt: IconType;
export declare const BiSolidCoffeeBean: IconType;
export declare const BiSolidCoffeeTogo: IconType;
export declare const BiSolidCoffee: IconType;
export declare const BiSolidCog: IconType;
export declare const BiSolidCoinStack: IconType;
export declare const BiSolidCoin: IconType;
export declare const BiSolidCollection: IconType;
export declare const BiSolidColorFill: IconType;
export declare const BiSolidColor: IconType;
export declare const BiSolidCommentAdd: IconType;
export declare const BiSolidCommentCheck: IconType;
export declare const BiSolidCommentDetail: IconType;
export declare const BiSolidCommentDots: IconType;
export declare const BiSolidCommentEdit: IconType;
export declare const BiSolidCommentError: IconType;
export declare const BiSolidCommentMinus: IconType;
export declare const BiSolidCommentX: IconType;
export declare const BiSolidComment: IconType;
export declare const BiSolidCompass: IconType;
export declare const BiSolidComponent: IconType;
export declare const BiSolidConfused: IconType;
export declare const BiSolidContact: IconType;
export declare const BiSolidConversation: IconType;
export declare const BiSolidCookie: IconType;
export declare const BiSolidCool: IconType;
export declare const BiSolidCopyAlt: IconType;
export declare const BiSolidCopy: IconType;
export declare const BiSolidCopyright: IconType;
export declare const BiSolidCoupon: IconType;
export declare const BiSolidCreditCardAlt: IconType;
export declare const BiSolidCreditCardFront: IconType;
export declare const BiSolidCreditCard: IconType;
export declare const BiSolidCricketBall: IconType;
export declare const BiSolidCrop: IconType;
export declare const BiSolidCrown: IconType;
export declare const BiSolidCubeAlt: IconType;
export declare const BiSolidCube: IconType;
export declare const BiSolidCuboid: IconType;
export declare const BiSolidCustomize: IconType;
export declare const BiSolidCylinder: IconType;
export declare const BiSolidDashboard: IconType;
export declare const BiSolidData: IconType;
export declare const BiSolidDetail: IconType;
export declare const BiSolidDevices: IconType;
export declare const BiSolidDiamond: IconType;
export declare const BiSolidDice1: IconType;
export declare const BiSolidDice2: IconType;
export declare const BiSolidDice3: IconType;
export declare const BiSolidDice4: IconType;
export declare const BiSolidDice5: IconType;
export declare const BiSolidDice6: IconType;
export declare const BiSolidDirectionLeft: IconType;
export declare const BiSolidDirectionRight: IconType;
export declare const BiSolidDirections: IconType;
export declare const BiSolidDisc: IconType;
export declare const BiSolidDiscount: IconType;
export declare const BiSolidDish: IconType;
export declare const BiSolidDislike: IconType;
export declare const BiSolidDizzy: IconType;
export declare const BiSolidDockBottom: IconType;
export declare const BiSolidDockLeft: IconType;
export declare const BiSolidDockRight: IconType;
export declare const BiSolidDockTop: IconType;
export declare const BiSolidDog: IconType;
export declare const BiSolidDollarCircle: IconType;
export declare const BiSolidDonateBlood: IconType;
export declare const BiSolidDonateHeart: IconType;
export declare const BiSolidDoorOpen: IconType;
export declare const BiSolidDoughnutChart: IconType;
export declare const BiSolidDownArrowAlt: IconType;
export declare const BiSolidDownArrowCircle: IconType;
export declare const BiSolidDownArrowSquare: IconType;
export declare const BiSolidDownArrow: IconType;
export declare const BiSolidDownload: IconType;
export declare const BiSolidDownvote: IconType;
export declare const BiSolidDrink: IconType;
export declare const BiSolidDropletHalf: IconType;
export declare const BiSolidDroplet: IconType;
export declare const BiSolidDryer: IconType;
export declare const BiSolidDuplicate: IconType;
export declare const BiSolidEditAlt: IconType;
export declare const BiSolidEditLocation: IconType;
export declare const BiSolidEdit: IconType;
export declare const BiSolidEject: IconType;
export declare const BiSolidEnvelopeOpen: IconType;
export declare const BiSolidEnvelope: IconType;
export declare const BiSolidEraser: IconType;
export declare const BiSolidErrorAlt: IconType;
export declare const BiSolidErrorCircle: IconType;
export declare const BiSolidError: IconType;
export declare const BiSolidEvStation: IconType;
export declare const BiSolidExit: IconType;
export declare const BiSolidExtension: IconType;
export declare const BiSolidEyedropper: IconType;
export declare const BiSolidFaceMask: IconType;
export declare const BiSolidFace: IconType;
export declare const BiSolidFactory: IconType;
export declare const BiSolidFastForwardCircle: IconType;
export declare const BiSolidFileArchive: IconType;
export declare const BiSolidFileBlank: IconType;
export declare const BiSolidFileCss: IconType;
export declare const BiSolidFileDoc: IconType;
export declare const BiSolidFileExport: IconType;
export declare const BiSolidFileFind: IconType;
export declare const BiSolidFileGif: IconType;
export declare const BiSolidFileHtml: IconType;
export declare const BiSolidFileImage: IconType;
export declare const BiSolidFileImport: IconType;
export declare const BiSolidFileJpg: IconType;
export declare const BiSolidFileJs: IconType;
export declare const BiSolidFileJson: IconType;
export declare const BiSolidFileMd: IconType;
export declare const BiSolidFilePdf: IconType;
export declare const BiSolidFilePlus: IconType;
export declare const BiSolidFilePng: IconType;
export declare const BiSolidFileTxt: IconType;
export declare const BiSolidFile: IconType;
export declare const BiSolidFilm: IconType;
export declare const BiSolidFilterAlt: IconType;
export declare const BiSolidFirstAid: IconType;
export declare const BiSolidFlagAlt: IconType;
export declare const BiSolidFlagCheckered: IconType;
export declare const BiSolidFlag: IconType;
export declare const BiSolidFlame: IconType;
export declare const BiSolidFlask: IconType;
export declare const BiSolidFlorist: IconType;
export declare const BiSolidFolderMinus: IconType;
export declare const BiSolidFolderOpen: IconType;
export declare const BiSolidFolderPlus: IconType;
export declare const BiSolidFolder: IconType;
export declare const BiSolidFoodMenu: IconType;
export declare const BiSolidFridge: IconType;
export declare const BiSolidGame: IconType;
export declare const BiSolidGasPump: IconType;
export declare const BiSolidGhost: IconType;
export declare const BiSolidGift: IconType;
export declare const BiSolidGraduation: IconType;
export declare const BiSolidGridAlt: IconType;
export declare const BiSolidGrid: IconType;
export declare const BiSolidGroup: IconType;
export declare const BiSolidGuitarAmp: IconType;
export declare const BiSolidHandDown: IconType;
export declare const BiSolidHandLeft: IconType;
export declare const BiSolidHandRight: IconType;
export declare const BiSolidHandUp: IconType;
export declare const BiSolidHand: IconType;
export declare const BiSolidHappyAlt: IconType;
export declare const BiSolidHappyBeaming: IconType;
export declare const BiSolidHappyHeartEyes: IconType;
export declare const BiSolidHappy: IconType;
export declare const BiSolidHardHat: IconType;
export declare const BiSolidHdd: IconType;
export declare const BiSolidHeartCircle: IconType;
export declare const BiSolidHeartSquare: IconType;
export declare const BiSolidHeart: IconType;
export declare const BiSolidHelpCircle: IconType;
export declare const BiSolidHide: IconType;
export declare const BiSolidHomeAlt2: IconType;
export declare const BiSolidHomeCircle: IconType;
export declare const BiSolidHomeHeart: IconType;
export declare const BiSolidHomeSmile: IconType;
export declare const BiSolidHome: IconType;
export declare const BiSolidHot: IconType;
export declare const BiSolidHotel: IconType;
export declare const BiSolidHourglassBottom: IconType;
export declare const BiSolidHourglassTop: IconType;
export declare const BiSolidHourglass: IconType;
export declare const BiSolidIdCard: IconType;
export declare const BiSolidImageAdd: IconType;
export declare const BiSolidImageAlt: IconType;
export declare const BiSolidImage: IconType;
export declare const BiSolidInbox: IconType;
export declare const BiSolidInfoCircle: IconType;
export declare const BiSolidInfoSquare: IconType;
export declare const BiSolidInjection: IconType;
export declare const BiSolidInstitution: IconType;
export declare const BiSolidInvader: IconType;
export declare const BiSolidJoystickAlt: IconType;
export declare const BiSolidJoystickButton: IconType;
export declare const BiSolidJoystick: IconType;
export declare const BiSolidKey: IconType;
export declare const BiSolidKeyboard: IconType;
export declare const BiSolidLabel: IconType;
export declare const BiSolidLandmark: IconType;
export declare const BiSolidLandscape: IconType;
export declare const BiSolidLaugh: IconType;
export declare const BiSolidLayerMinus: IconType;
export declare const BiSolidLayerPlus: IconType;
export declare const BiSolidLayer: IconType;
export declare const BiSolidLayout: IconType;
export declare const BiSolidLeaf: IconType;
export declare const BiSolidLeftArrowAlt: IconType;
export declare const BiSolidLeftArrowCircle: IconType;
export declare const BiSolidLeftArrowSquare: IconType;
export declare const BiSolidLeftArrow: IconType;
export declare const BiSolidLeftDownArrowCircle: IconType;
export declare const BiSolidLeftTopArrowCircle: IconType;
export declare const BiSolidLemon: IconType;
export declare const BiSolidLike: IconType;
export declare const BiSolidLocationPlus: IconType;
export declare const BiSolidLockAlt: IconType;
export declare const BiSolidLockOpenAlt: IconType;
export declare const BiSolidLockOpen: IconType;
export declare const BiSolidLock: IconType;
export declare const BiSolidLogInCircle: IconType;
export declare const BiSolidLogIn: IconType;
export declare const BiSolidLogOutCircle: IconType;
export declare const BiSolidLogOut: IconType;
export declare const BiSolidLowVision: IconType;
export declare const BiSolidMagicWand: IconType;
export declare const BiSolidMagnet: IconType;
export declare const BiSolidMapAlt: IconType;
export declare const BiSolidMapPin: IconType;
export declare const BiSolidMap: IconType;
export declare const BiSolidMask: IconType;
export declare const BiSolidMedal: IconType;
export declare const BiSolidMegaphone: IconType;
export declare const BiSolidMehAlt: IconType;
export declare const BiSolidMehBlank: IconType;
export declare const BiSolidMeh: IconType;
export declare const BiSolidMemoryCard: IconType;
export declare const BiSolidMessageAdd: IconType;
export declare const BiSolidMessageAltAdd: IconType;
export declare const BiSolidMessageAltCheck: IconType;
export declare const BiSolidMessageAltDetail: IconType;
export declare const BiSolidMessageAltDots: IconType;
export declare const BiSolidMessageAltEdit: IconType;
export declare const BiSolidMessageAltError: IconType;
export declare const BiSolidMessageAltMinus: IconType;
export declare const BiSolidMessageAltX: IconType;
export declare const BiSolidMessageAlt: IconType;
export declare const BiSolidMessageCheck: IconType;
export declare const BiSolidMessageDetail: IconType;
export declare const BiSolidMessageDots: IconType;
export declare const BiSolidMessageEdit: IconType;
export declare const BiSolidMessageError: IconType;
export declare const BiSolidMessageMinus: IconType;
export declare const BiSolidMessageRoundedAdd: IconType;
export declare const BiSolidMessageRoundedCheck: IconType;
export declare const BiSolidMessageRoundedDetail: IconType;
export declare const BiSolidMessageRoundedDots: IconType;
export declare const BiSolidMessageRoundedEdit: IconType;
export declare const BiSolidMessageRoundedError: IconType;
export declare const BiSolidMessageRoundedMinus: IconType;
export declare const BiSolidMessageRoundedX: IconType;
export declare const BiSolidMessageRounded: IconType;
export declare const BiSolidMessageSquareAdd: IconType;
export declare const BiSolidMessageSquareCheck: IconType;
export declare const BiSolidMessageSquareDetail: IconType;
export declare const BiSolidMessageSquareDots: IconType;
export declare const BiSolidMessageSquareEdit: IconType;
export declare const BiSolidMessageSquareError: IconType;
export declare const BiSolidMessageSquareMinus: IconType;
export declare const BiSolidMessageSquareX: IconType;
export declare const BiSolidMessageSquare: IconType;
export declare const BiSolidMessageX: IconType;
export declare const BiSolidMessage: IconType;
export declare const BiSolidMeteor: IconType;
export declare const BiSolidMicrochip: IconType;
export declare const BiSolidMicrophoneAlt: IconType;
export declare const BiSolidMicrophoneOff: IconType;
export declare const BiSolidMicrophone: IconType;
export declare const BiSolidMinusCircle: IconType;
export declare const BiSolidMinusSquare: IconType;
export declare const BiSolidMobileVibration: IconType;
export declare const BiSolidMobile: IconType;
export declare const BiSolidMoon: IconType;
export declare const BiSolidMouseAlt: IconType;
export declare const BiSolidMouse: IconType;
export declare const BiSolidMoviePlay: IconType;
export declare const BiSolidMovie: IconType;
export declare const BiSolidMusic: IconType;
export declare const BiSolidNavigation: IconType;
export declare const BiSolidNetworkChart: IconType;
export declare const BiSolidNews: IconType;
export declare const BiSolidNoEntry: IconType;
export declare const BiSolidNote: IconType;
export declare const BiSolidNotepad: IconType;
export declare const BiSolidNotificationOff: IconType;
export declare const BiSolidNotification: IconType;
export declare const BiSolidObjectsHorizontalCenter: IconType;
export declare const BiSolidObjectsHorizontalLeft: IconType;
export declare const BiSolidObjectsHorizontalRight: IconType;
export declare const BiSolidObjectsVerticalBottom: IconType;
export declare const BiSolidObjectsVerticalCenter: IconType;
export declare const BiSolidObjectsVerticalTop: IconType;
export declare const BiSolidOffer: IconType;
export declare const BiSolidPackage: IconType;
export declare const BiSolidPaintRoll: IconType;
export declare const BiSolidPaint: IconType;
export declare const BiSolidPalette: IconType;
export declare const BiSolidPaperPlane: IconType;
export declare const BiSolidParking: IconType;
export declare const BiSolidParty: IconType;
export declare const BiSolidPaste: IconType;
export declare const BiSolidPear: IconType;
export declare const BiSolidPen: IconType;
export declare const BiSolidPencil: IconType;
export declare const BiSolidPhoneCall: IconType;
export declare const BiSolidPhoneIncoming: IconType;
export declare const BiSolidPhoneOff: IconType;
export declare const BiSolidPhoneOutgoing: IconType;
export declare const BiSolidPhone: IconType;
export declare const BiSolidPhotoAlbum: IconType;
export declare const BiSolidPiano: IconType;
export declare const BiSolidPieChartAlt2: IconType;
export declare const BiSolidPieChartAlt: IconType;
export declare const BiSolidPieChart: IconType;
export declare const BiSolidPin: IconType;
export declare const BiSolidPizza: IconType;
export declare const BiSolidPlaneAlt: IconType;
export declare const BiSolidPlaneLand: IconType;
export declare const BiSolidPlaneTakeOff: IconType;
export declare const BiSolidPlane: IconType;
export declare const BiSolidPlanet: IconType;
export declare const BiSolidPlaylist: IconType;
export declare const BiSolidPlug: IconType;
export declare const BiSolidPlusCircle: IconType;
export declare const BiSolidPlusSquare: IconType;
export declare const BiSolidPointer: IconType;
export declare const BiSolidPolygon: IconType;
export declare const BiSolidPopsicle: IconType;
export declare const BiSolidPrinter: IconType;
export declare const BiSolidPurchaseTagAlt: IconType;
export declare const BiSolidPurchaseTag: IconType;
export declare const BiSolidPyramid: IconType;
export declare const BiSolidQuoteAltLeft: IconType;
export declare const BiSolidQuoteAltRight: IconType;
export declare const BiSolidQuoteLeft: IconType;
export declare const BiSolidQuoteRight: IconType;
export declare const BiSolidQuoteSingleLeft: IconType;
export declare const BiSolidQuoteSingleRight: IconType;
export declare const BiSolidRadiation: IconType;
export declare const BiSolidRadio: IconType;
export declare const BiSolidReceipt: IconType;
export declare const BiSolidRectangle: IconType;
export declare const BiSolidRegistered: IconType;
export declare const BiSolidRename: IconType;
export declare const BiSolidReport: IconType;
export declare const BiSolidRewindCircle: IconType;
export declare const BiSolidRightArrowAlt: IconType;
export declare const BiSolidRightArrowCircle: IconType;
export declare const BiSolidRightArrowSquare: IconType;
export declare const BiSolidRightArrow: IconType;
export declare const BiSolidRightDownArrowCircle: IconType;
export declare const BiSolidRightTopArrowCircle: IconType;
export declare const BiSolidRocket: IconType;
export declare const BiSolidRuler: IconType;
export declare const BiSolidSad: IconType;
export declare const BiSolidSave: IconType;
export declare const BiSolidSchool: IconType;
export declare const BiSolidSearchAlt2: IconType;
export declare const BiSolidSearch: IconType;
export declare const BiSolidSelectMultiple: IconType;
export declare const BiSolidSend: IconType;
export declare const BiSolidServer: IconType;
export declare const BiSolidShapes: IconType;
export declare const BiSolidShareAlt: IconType;
export declare const BiSolidShare: IconType;
export declare const BiSolidShieldAlt2: IconType;
export declare const BiSolidShieldMinus: IconType;
export declare const BiSolidShieldPlus: IconType;
export declare const BiSolidShieldX: IconType;
export declare const BiSolidShield: IconType;
export declare const BiSolidShip: IconType;
export declare const BiSolidShocked: IconType;
export declare const BiSolidShoppingBagAlt: IconType;
export declare const BiSolidShoppingBag: IconType;
export declare const BiSolidShoppingBags: IconType;
export declare const BiSolidShow: IconType;
export declare const BiSolidShower: IconType;
export declare const BiSolidSkipNextCircle: IconType;
export declare const BiSolidSkipPreviousCircle: IconType;
export declare const BiSolidSkull: IconType;
export declare const BiSolidSleepy: IconType;
export declare const BiSolidSlideshow: IconType;
export declare const BiSolidSmile: IconType;
export declare const BiSolidSortAlt: IconType;
export declare const BiSolidSpa: IconType;
export declare const BiSolidSpeaker: IconType;
export declare const BiSolidSprayCan: IconType;
export declare const BiSolidSpreadsheet: IconType;
export declare const BiSolidSquareRounded: IconType;
export declare const BiSolidSquare: IconType;
export declare const BiSolidStarHalf: IconType;
export declare const BiSolidStar: IconType;
export declare const BiSolidSticker: IconType;
export declare const BiSolidStopwatch: IconType;
export declare const BiSolidStoreAlt: IconType;
export declare const BiSolidStore: IconType;
export declare const BiSolidSun: IconType;
export declare const BiSolidSushi: IconType;
export declare const BiSolidTShirt: IconType;
export declare const BiSolidTachometer: IconType;
export declare const BiSolidTagAlt: IconType;
export declare const BiSolidTagX: IconType;
export declare const BiSolidTag: IconType;
export declare const BiSolidTaxi: IconType;
export declare const BiSolidTennisBall: IconType;
export declare const BiSolidTerminal: IconType;
export declare const BiSolidThermometer: IconType;
export declare const BiSolidTimeFive: IconType;
export declare const BiSolidTime: IconType;
export declare const BiSolidTimer: IconType;
export declare const BiSolidTired: IconType;
export declare const BiSolidToTop: IconType;
export declare const BiSolidToggleLeft: IconType;
export declare const BiSolidToggleRight: IconType;
export declare const BiSolidTone: IconType;
export declare const BiSolidTorch: IconType;
export declare const BiSolidTrafficBarrier: IconType;
export declare const BiSolidTrafficCone: IconType;
export declare const BiSolidTraffic: IconType;
export declare const BiSolidTrain: IconType;
export declare const BiSolidTrashAlt: IconType;
export declare const BiSolidTrash: IconType;
export declare const BiSolidTreeAlt: IconType;
export declare const BiSolidTree: IconType;
export declare const BiSolidTrophy: IconType;
export declare const BiSolidTruck: IconType;
export declare const BiSolidTv: IconType;
export declare const BiSolidUniversalAccess: IconType;
export declare const BiSolidUpArrowAlt: IconType;
export declare const BiSolidUpArrowCircle: IconType;
export declare const BiSolidUpArrowSquare: IconType;
export declare const BiSolidUpArrow: IconType;
export declare const BiSolidUpsideDown: IconType;
export declare const BiSolidUpvote: IconType;
export declare const BiSolidUserAccount: IconType;
export declare const BiSolidUserBadge: IconType;
export declare const BiSolidUserCheck: IconType;
export declare const BiSolidUserCircle: IconType;
export declare const BiSolidUserDetail: IconType;
export declare const BiSolidUserMinus: IconType;
export declare const BiSolidUserPin: IconType;
export declare const BiSolidUserPlus: IconType;
export declare const BiSolidUserRectangle: IconType;
export declare const BiSolidUserVoice: IconType;
export declare const BiSolidUserX: IconType;
export declare const BiSolidUser: IconType;
export declare const BiSolidVector: IconType;
export declare const BiSolidVial: IconType;
export declare const BiSolidVideoOff: IconType;
export declare const BiSolidVideoPlus: IconType;
export declare const BiSolidVideoRecording: IconType;
export declare const BiSolidVideo: IconType;
export declare const BiSolidVideos: IconType;
export declare const BiSolidVirusBlock: IconType;
export declare const BiSolidVirus: IconType;
export declare const BiSolidVolumeFull: IconType;
export declare const BiSolidVolumeLow: IconType;
export declare const BiSolidVolumeMute: IconType;
export declare const BiSolidVolume: IconType;
export declare const BiSolidWalletAlt: IconType;
export declare const BiSolidWallet: IconType;
export declare const BiSolidWasher: IconType;
export declare const BiSolidWatchAlt: IconType;
export declare const BiSolidWatch: IconType;
export declare const BiSolidWebcam: IconType;
export declare const BiSolidWidget: IconType;
export declare const BiSolidWindowAlt: IconType;
export declare const BiSolidWine: IconType;
export declare const BiSolidWinkSmile: IconType;
export declare const BiSolidWinkTongue: IconType;
export declare const BiSolidWrench: IconType;
export declare const BiSolidXCircle: IconType;
export declare const BiSolidXSquare: IconType;
export declare const BiSolidYinYang: IconType;
export declare const BiSolidZap: IconType;
export declare const BiSolidZoomIn: IconType;
export declare const BiSolidZoomOut: IconType;
export declare const BiLogo500Px: IconType;
export declare const BiLogo99Designs: IconType;
export declare const BiLogoAdobe: IconType;
export declare const BiLogoAirbnb: IconType;
export declare const BiLogoAlgolia: IconType;
export declare const BiLogoAmazon: IconType;
export declare const BiLogoAndroid: IconType;
export declare const BiLogoAngular: IconType;
export declare const BiLogoApple: IconType;
export declare const BiLogoAudible: IconType;
export declare const BiLogoAws: IconType;
export declare const BiLogoBaidu: IconType;
export declare const BiLogoBehance: IconType;
export declare const BiLogoBing: IconType;
export declare const BiLogoBitcoin: IconType;
export declare const BiLogoBlender: IconType;
export declare const BiLogoBlogger: IconType;
export declare const BiLogoBootstrap: IconType;
export declare const BiLogoCPlusPlus: IconType;
export declare const BiLogoChrome: IconType;
export declare const BiLogoCodepen: IconType;
export declare const BiLogoCreativeCommons: IconType;
export declare const BiLogoCss3: IconType;
export declare const BiLogoDailymotion: IconType;
export declare const BiLogoDeezer: IconType;
export declare const BiLogoDevTo: IconType;
export declare const BiLogoDeviantart: IconType;
export declare const BiLogoDigg: IconType;
export declare const BiLogoDigitalocean: IconType;
export declare const BiLogoDiscordAlt: IconType;
export declare const BiLogoDiscord: IconType;
export declare const BiLogoDiscourse: IconType;
export declare const BiLogoDjango: IconType;
export declare const BiLogoDocker: IconType;
export declare const BiLogoDribbble: IconType;
export declare const BiLogoDropbox: IconType;
export declare const BiLogoDrupal: IconType;
export declare const BiLogoEbay: IconType;
export declare const BiLogoEdge: IconType;
export declare const BiLogoEtsy: IconType;
export declare const BiLogoFacebookCircle: IconType;
export declare const BiLogoFacebookSquare: IconType;
export declare const BiLogoFacebook: IconType;
export declare const BiLogoFigma: IconType;
export declare const BiLogoFirebase: IconType;
export declare const BiLogoFirefox: IconType;
export declare const BiLogoFlask: IconType;
export declare const BiLogoFlickrSquare: IconType;
export declare const BiLogoFlickr: IconType;
export declare const BiLogoFlutter: IconType;
export declare const BiLogoFoursquare: IconType;
export declare const BiLogoGit: IconType;
export declare const BiLogoGithub: IconType;
export declare const BiLogoGitlab: IconType;
export declare const BiLogoGmail: IconType;
export declare const BiLogoGoLang: IconType;
export declare const BiLogoGoogleCloud: IconType;
export declare const BiLogoGooglePlusCircle: IconType;
export declare const BiLogoGooglePlus: IconType;
export declare const BiLogoGoogle: IconType;
export declare const BiLogoGraphql: IconType;
export declare const BiLogoHeroku: IconType;
export declare const BiLogoHtml5: IconType;
export declare const BiLogoImdb: IconType;
export declare const BiLogoInstagramAlt: IconType;
export declare const BiLogoInstagram: IconType;
export declare const BiLogoInternetExplorer: IconType;
export declare const BiLogoInvision: IconType;
export declare const BiLogoJava: IconType;
export declare const BiLogoJavascript: IconType;
export declare const BiLogoJoomla: IconType;
export declare const BiLogoJquery: IconType;
export declare const BiLogoJsfiddle: IconType;
export declare const BiLogoKickstarter: IconType;
export declare const BiLogoKubernetes: IconType;
export declare const BiLogoLess: IconType;
export declare const BiLogoLinkedinSquare: IconType;
export declare const BiLogoLinkedin: IconType;
export declare const BiLogoMagento: IconType;
export declare const BiLogoMailchimp: IconType;
export declare const BiLogoMarkdown: IconType;
export declare const BiLogoMastercard: IconType;
export declare const BiLogoMastodon: IconType;
export declare const BiLogoMediumOld: IconType;
export declare const BiLogoMediumSquare: IconType;
export declare const BiLogoMedium: IconType;
export declare const BiLogoMessenger: IconType;
export declare const BiLogoMeta: IconType;
export declare const BiLogoMicrosoftTeams: IconType;
export declare const BiLogoMicrosoft: IconType;
export declare const BiLogoMongodb: IconType;
export declare const BiLogoNetlify: IconType;
export declare const BiLogoNodejs: IconType;
export declare const BiLogoOkRu: IconType;
export declare const BiLogoOpera: IconType;
export declare const BiLogoPatreon: IconType;
export declare const BiLogoPaypal: IconType;
export declare const BiLogoPeriscope: IconType;
export declare const BiLogoPhp: IconType;
export declare const BiLogoPinterestAlt: IconType;
export declare const BiLogoPinterest: IconType;
export declare const BiLogoPlayStore: IconType;
export declare const BiLogoPocket: IconType;
export declare const BiLogoPostgresql: IconType;
export declare const BiLogoProductHunt: IconType;
export declare const BiLogoPython: IconType;
export declare const BiLogoQuora: IconType;
export declare const BiLogoReact: IconType;
export declare const BiLogoRedbubble: IconType;
export declare const BiLogoReddit: IconType;
export declare const BiLogoRedux: IconType;
export declare const BiLogoSass: IconType;
export declare const BiLogoShopify: IconType;
export declare const BiLogoSketch: IconType;
export declare const BiLogoSkype: IconType;
export declare const BiLogoSlackOld: IconType;
export declare const BiLogoSlack: IconType;
export declare const BiLogoSnapchat: IconType;
export declare const BiLogoSoundcloud: IconType;
export declare const BiLogoSpotify: IconType;
export declare const BiLogoSpringBoot: IconType;
export declare const BiLogoSquarespace: IconType;
export declare const BiLogoStackOverflow: IconType;
export declare const BiLogoSteam: IconType;
export declare const BiLogoStripe: IconType;
export declare const BiLogoTailwindCss: IconType;
export declare const BiLogoTelegram: IconType;
export declare const BiLogoTiktok: IconType;
export declare const BiLogoTrello: IconType;
export declare const BiLogoTripAdvisor: IconType;
export declare const BiLogoTumblr: IconType;
export declare const BiLogoTux: IconType;
export declare const BiLogoTwitch: IconType;
export declare const BiLogoTwitter: IconType;
export declare const BiLogoTypescript: IconType;
export declare const BiLogoUnity: IconType;
export declare const BiLogoUnsplash: IconType;
export declare const BiLogoUpwork: IconType;
export declare const BiLogoVenmo: IconType;
export declare const BiLogoVimeo: IconType;
export declare const BiLogoVisa: IconType;
export declare const BiLogoVisualStudio: IconType;
export declare const BiLogoVk: IconType;
export declare const BiLogoVuejs: IconType;
export declare const BiLogoWhatsappSquare: IconType;
export declare const BiLogoWhatsapp: IconType;
export declare const BiLogoWikipedia: IconType;
export declare const BiLogoWindows: IconType;
export declare const BiLogoWix: IconType;
export declare const BiLogoWordpress: IconType;
export declare const BiLogoXing: IconType;
export declare const BiLogoYahoo: IconType;
export declare const BiLogoYelp: IconType;
export declare const BiLogoYoutube: IconType;
export declare const BiLogoZoom: IconType;

{"name": "@types/react-syntax-highlighter", "version": "15.5.13", "description": "TypeScript definitions for react-syntax-highlighter", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-syntax-highlighter", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "NoHomey", "url": "https://github.com/NoHomey"}, {"name": "<PERSON>", "githubUsername": "guoyunhe", "url": "https://github.com/guoyunhe"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "anirban09", "url": "https://github.com/anirban09"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/micha<PERSON>uen"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "DoK6n", "url": "https://github.com/DoK6n"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-syntax-highlighter"}, "scripts": {}, "dependencies": {"@types/react": "*"}, "typesPublisherContentHash": "2e44cab3fa3aa5dc293cad2f7576268101c60aa583a480d312c84c54aee7fecb", "typeScriptVersion": "4.7"}
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Heetch Dark
  Author: <PERSON> (<EMAIL>)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme heetch-dark
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #190134  Default Background
base01  #392551  Lighter Background (Used for status bars, line number and folding marks)
base02  #5A496E  Selection Background
base03  #7B6D8B  Comments, Invisibles, Line Highlighting
base04  #9C92A8  Dark Foreground (Used for status bars)
base05  #BDB6C5  Default Foreground, Caret, Delimiters, Operators
base06  #DEDAE2  Light Foreground (Not often used)
base07  #FEFFFF  Light Background (Not often used)
base08  #27D9D5  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #5BA2B6  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #8F6C97  Classes, Markup Bold, Search Text Background
base0B  #C33678  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #F80059  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #BD0152  Functions, Methods, Attribute IDs, Headings
base0E  #82034C  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #470546  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #BDB6C5;
  background: #190134
}
.hljs::selection,
.hljs ::selection {
  background-color: #5A496E;
  color: #BDB6C5
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #7B6D8B -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #7B6D8B
}
/* base04 - #9C92A8 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #9C92A8
}
/* base05 - #BDB6C5 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #BDB6C5
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #27D9D5
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #5BA2B6
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #8F6C97
}
.hljs-strong {
  font-weight: bold;
  color: #8F6C97
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #C33678
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #F80059
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #BD0152
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #82034C
}
.hljs-emphasis {
  color: #82034C;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #470546
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}
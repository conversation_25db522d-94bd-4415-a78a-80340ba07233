import base64
import os
import uuid
import logging
import aiofiles
import re
import json
from typing import List, Optional
from fastapi import UploadFile
from PIL import Image
from datetime import datetime

from openai import OpenAI

from app.schemas.images import ImageUploadResponse, ImageAnalysisResponse

# 获取项目根目录
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))

# 环境变量配置
VLLM_API_KEY = os.getenv("API_KEY_VLLM_INTERNVL", "your-api-key-here")
VLLM_API_URL = "https://chat.intern-ai.org.cn/api/v1/"
VLLM_API_MODEL = "intern-latest"


class ImageService:
    """图片服务，处理图片上传和分析"""
    
    def __init__(self):
        """初始化图片服务"""
        self.logger = logging.getLogger("image_service")
        
        # 确保图片存储目录存在
        self.images_dir = os.path.join(BASE_DIR, "data", "images")
        os.makedirs(self.images_dir, exist_ok=True)
        
        # 初始化日志
        logs_dir = os.path.join(BASE_DIR, "logs")
        os.makedirs(logs_dir, exist_ok=True)
        
        # 配置日志
        if not self.logger.handlers:
            file_handler = logging.FileHandler(os.path.join(logs_dir, "images.log"))
            file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
            self.logger.addHandler(file_handler)
            self.logger.setLevel(logging.INFO)

    def extract_json_from_text(self, text: str) -> Optional[str]:
        """从大模型返回的文本中提取JSON代码块

        Args:
            text: 大模型返回的原始文本

        Returns:
            提取出的JSON字符串，如果没有找到则返回None
        """
        try:
            # 匹配 ```json 和 ``` 之间的内容
            json_pattern = r'```json\s*(.*?)\s*```'
            matches = re.findall(json_pattern, text, re.DOTALL | re.IGNORECASE)

            if matches:
                # 取第一个匹配的JSON代码块
                json_str = matches[0].strip()

                # 验证JSON格式是否正确
                try:
                    json.loads(json_str)
                    return json_str
                except json.JSONDecodeError:
                    self.logger.warning(f"Invalid JSON format in extracted text: {json_str}")
                    return None

            # 如果没有找到 ```json 格式，尝试匹配普通的 ``` 代码块
            code_pattern = r'```\s*(.*?)\s*```'
            matches = re.findall(code_pattern, text, re.DOTALL)

            for match in matches:
                match = match.strip()
                # 检查是否是JSON格式
                if (match.startswith('[') and match.endswith(']')) or \
                   (match.startswith('{') and match.endswith('}')):
                    try:
                        json.loads(match)
                        return match
                    except json.JSONDecodeError:
                        continue

            # 如果都没有找到，尝试直接从文本中提取JSON
            # 查找以 [ 或 { 开始的JSON结构
            json_start_pattern = r'(\[.*?\]|\{.*?\})'
            matches = re.findall(json_start_pattern, text, re.DOTALL)

            for match in matches:
                try:
                    json.loads(match)
                    return match
                except json.JSONDecodeError:
                    continue

            return None

        except Exception as e:
            self.logger.error(f"Error extracting JSON from text: {e}")
            return None

    def format_json_with_stats(self, json_str: str) -> tuple[str, int]:
        """格式化JSON并统计物体数量

        Args:
            json_str: 原始JSON字符串

        Returns:
            tuple: (格式化的JSON字符串, 物体数量)
        """
        try:
            # 解析JSON
            data = json.loads(json_str)

            # 统计物体数量
            object_count = 0
            if isinstance(data, list):
                object_count = len(data)
            elif isinstance(data, dict):
                object_count = 1

            # 格式化JSON，使用3个空格缩进
            formatted_json = json.dumps(data, ensure_ascii=False, indent=3)

            return formatted_json, object_count

        except Exception as e:
            self.logger.error(f"Error formatting JSON: {e}")
            return json_str, 0

    async def upload_image(self, file: UploadFile) -> ImageUploadResponse:
        """上传图片
        
        Args:
            file: 上传的图片文件
            
        Returns:
            图片上传响应
        """
        # 验证文件类型
        if not file.content_type or not file.content_type.startswith('image/'):
            raise ValueError("Invalid file type. Only images are allowed.")
        
        # 生成唯一文件名
        file_ext = os.path.splitext(file.filename)[1] if file.filename else ".jpg"
        unique_filename = f"{uuid.uuid4()}{file_ext}"
        
        # 构建文件路径
        file_path = os.path.join(self.images_dir, unique_filename)
        
        # 保存文件
        try:
            async with aiofiles.open(file_path, 'wb') as out_file:
                content = await file.read()
                await out_file.write(content)
            
            # 验证图片文件
            try:
                with Image.open(file_path) as img:
                    img.verify()
            except Exception as e:
                # 如果图片无效，删除文件
                if os.path.exists(file_path):
                    os.remove(file_path)
                raise ValueError(f"Invalid image file: {e}")
            
            # 构建URL
            url = f"/data/images/{unique_filename}"
            
            self.logger.info(f"Uploaded image: {unique_filename}")
            
            return ImageUploadResponse(
                url=url,
                filename=unique_filename
            )
        except Exception as e:
            self.logger.error(f"Failed to upload image: {e}")
            raise

    async def analyze_image(self, image_url: str, prompt: str = "从多个角度描述该图像") -> ImageAnalysisResponse:
        """分析图片
        
        Args:
            image_url: 图片URL
            prompt: 分析提示词
            
        Returns:
            图片分析响应
        """
        try:
            # 从URL中提取文件名
            filename = image_url.split("/")[-1]
            file_path = os.path.join(self.images_dir, filename)
            
            if not os.path.exists(file_path):
                self.logger.error(f"Image file not found: {file_path}")
                raise FileNotFoundError(f"Image file not found: {filename}")

            prompt = '''
                你的任务是分析图像，判断在公路上是否有动物、抛洒物等异常物体，以JSON格式输出以及这些异常物体的名称、描述以及大致的位置和大小信息。
                **# 指令:**
                1.  严格按照下面定义的JSON Schema格式进行输出。
                2.  不要在JSON代码块前后添加任何解释、注释或其他多余的文字。
                3.  坐标和大小应该是0到100之间的相对值，其中(0,0)代表左上角。
                4.  如果文本中没有提到任何异常的物体，请返回一个空的JSON数组 `[]`。
                
                **# JSON Schema:**
                [
                  {
                    "object_name": "string", // 物体的名称
                    "bounding_box": {
                      "x": "integer",      // 物体左上角的X坐标 (0-100)
                      "y": "integer",      // 物体左上角的Y坐标 (0-100)
                      "width": "integer",   // 物体的相对宽度 (0-100)
                      "height": "integer"  // 物体的相对高度 (0-100)
                    },
                    "description": "string" // 对物体的简短描述
                  }
                ]
                
                **# 示例输出**:
                ```json
                [
                  {
                    "object_name": "猫",
                    "bounding_box": {
                      "x": 40,
                      "y": 45,
                      "width": 20,
                      "height": 15
                    },
                    "description": "一只正在睡觉的红色猫"
                  },
                  {
                    "object_name": "树枝",
                    "bounding_box": {
                      "x": 65,
                      "y": 50,
                      "width": 10,
                      "height": 10
                    },
                    "description": "散乱的树枝"
                  }
                ]
                '''
            # 调用多模态模型分析图片
            raw_response, image_b64_url = await self._describe_image(file_path, prompt)

            # 从原始响应中提取JSON代码块
            json_result = self.extract_json_from_text(raw_response)

            if json_result:
                # 格式化JSON并统计物体数量
                formatted_json, object_count = self.format_json_with_stats(json_result)

                # 添加统计信息到描述中
                stats_info = f"检测到 {object_count} 个异常物体\n\n"
                description = stats_info + formatted_json

                # 从JSON中提取标签
                tags = await self._extract_tags_from_json(json_result)
            else:
                # 如果没有提取到JSON，使用原始响应
                description = f"检测失败"
                # 提取关键标签
                # tags = await self._extract_tags(raw_response)

            self.logger.info(f"Analyzed image: {filename}")

            return ImageAnalysisResponse(
                description=description,
                tags=tags,
                image_url=image_url,
                image_base64_url=image_b64_url
            )
        except Exception as e:
            self.logger.error(f"Failed to analyze image: {e}")
            raise

    async def _describe_image(self, image_path: str, prompt: str) -> tuple[str, str]:
        """使用多模态模型描述图片"""
        try:
            with open(image_path, "rb") as image_file:
                image_b64 = base64.b64encode(image_file.read()).decode('utf-8')
            
            image_b64_url = f"data:image/png;base64,{image_b64}"
            
            client = OpenAI(api_key=VLLM_API_KEY, base_url=VLLM_API_URL)
            
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {"url": image_b64_url}
                        }
                    ]
                }
            ]
            
            completion = client.chat.completions.create(
                model=VLLM_API_MODEL,
                messages=messages,
                temperature=0.5,
                seed=0,
                top_p=0.70,
                stream=False
            )
            
            description = completion.choices[0].message.content
            return description, image_b64_url
            
        except Exception as e:
            self.logger.error(f"Failed to describe image: {e}")
            # 返回默认描述
            with open(image_path, "rb") as image_file:
                image_b64 = base64.b64encode(image_file.read()).decode('utf-8')
            image_b64_url = f"data:image/png;base64,{image_b64}"
            return "这是一张图片。由于技术原因，无法提供详细分析。", image_b64_url

    async def _extract_tags_from_json(self, json_str: str) -> List[str]:
        """从JSON结果中提取标签"""
        try:
            data = json.loads(json_str)
            tags = []

            if isinstance(data, list):
                for item in data:
                    if isinstance(item, dict):
                        # 从object_name中提取标签
                        if 'object_name' in item:
                            tags.append(item['object_name'])
                        # 从description中提取关键词
                        if 'description' in item:
                            desc_tags = await self._extract_tags(item['description'])
                            tags.extend(desc_tags)

            # 去重并限制数量
            unique_tags = list(dict.fromkeys(tags))  # 保持顺序的去重
            return unique_tags[:10]

        except Exception as e:
            self.logger.error(f"Failed to extract tags from JSON: {e}")
            return ["检测结果"]

    async def _extract_tags(self, description: str) -> List[str]:
        """从描述中提取标签"""
        try:
            # 简单的标签提取逻辑
            # 在实际应用中，可以使用更复杂的NLP技术
            tags = []

            # 基于关键词的简单标签提取
            keywords = ["人物", "动物", "建筑", "风景", "食物", "车辆", "植物", "天空", "水", "山", "城市", "室内", "室外",
                       "猫", "狗", "鸟", "马", "牛", "羊", "猪", "鸡", "鸭", "鹅", "兔", "鼠", "蛇", "鱼",
                       "汽车", "卡车", "摩托车", "自行车", "公交车", "火车", "飞机", "船", "树枝", "石头", "垃圾"]

            for keyword in keywords:
                if keyword in description:
                    tags.append(keyword)

            # 如果没有找到标签，添加默认标签
            if not tags:
                tags = ["图片", "内容"]

            return tags[:10]  # 最多返回10个标签

        except Exception as e:
            self.logger.error(f"Failed to extract tags: {e}")
            return ["图片", "内容"]

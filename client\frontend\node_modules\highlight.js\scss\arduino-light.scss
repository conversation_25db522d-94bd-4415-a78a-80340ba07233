pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*

Arduino® Light Theme - <PERSON><PERSON> <<EMAIL>>

*/
.hljs {
  background: white;
  color: #434f54
}
.hljs-subst {
  color: #434f54
}
.hljs-keyword,
.hljs-attribute,
.hljs-selector-tag,
.hljs-doctag,
.hljs-name {
  color: #00979D
}
.hljs-built_in,
.hljs-literal,
.hljs-bullet,
.hljs-code,
.hljs-addition {
  color: #D35400
}
.hljs-regexp,
.hljs-symbol,
.hljs-variable,
.hljs-template-variable,
.hljs-link,
.hljs-selector-attr,
.hljs-selector-pseudo {
  color: #00979D
}
.hljs-type,
.hljs-string,
.hljs-selector-id,
.hljs-selector-class,
.hljs-quote,
.hljs-template-tag,
.hljs-deletion {
  color: #005C5F
}
.hljs-comment {
  color: rgba(149,165,166,.8)
}
.hljs-meta .hljs-keyword {
  color: #728E00
}
.hljs-meta {
  color: #434f54
}
.hljs-emphasis {
  font-style: italic
}
.hljs-strong {
  font-weight: bold
}
.hljs-function {
  color: #728E00
}
.hljs-title,
.hljs-section {
  color: #880000;
  font-weight: bold
}
.hljs-number {
  color: #8A7B52
}
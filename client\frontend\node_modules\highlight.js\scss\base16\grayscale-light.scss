pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Grayscale Light
  Author: <PERSON> (https://github.com/Alexx2/)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme grayscale-light
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #f7f7f7  Default Background
base01  #e3e3e3  Lighter Background (Used for status bars, line number and folding marks)
base02  #b9b9b9  Selection Background
base03  #ababab  Comments, Invisibles, Line Highlighting
base04  #525252  Dark Foreground (Used for status bars)
base05  #464646  Default Foreground, Caret, Delimiters, Operators
base06  #252525  Light Foreground (Not often used)
base07  #101010  Light Background (Not often used)
base08  #7c7c7c  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #999999  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #a0a0a0  Classes, Markup Bold, Search Text Background
base0B  #8e8e8e  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #868686  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #686868  Functions, Methods, Attribute IDs, Headings
base0E  #747474  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #5e5e5e  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #464646;
  background: #f7f7f7
}
.hljs::selection,
.hljs ::selection {
  background-color: #b9b9b9;
  color: #464646
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #ababab -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #ababab
}
/* base04 - #525252 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #525252
}
/* base05 - #464646 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #464646
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #7c7c7c
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #999999
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #a0a0a0
}
.hljs-strong {
  font-weight: bold;
  color: #a0a0a0
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #8e8e8e
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #868686
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #686868
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #747474
}
.hljs-emphasis {
  color: #747474;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #5e5e5e
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}
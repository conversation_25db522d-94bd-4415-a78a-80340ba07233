"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ConversationPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileImage_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileImage,MessageSquare,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.321.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-image.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileImage_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileImage,MessageSquare,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.321.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileImage_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileImage,MessageSquare,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.321.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileImage_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileImage,MessageSquare,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.321.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Spin_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Spin,message!=!antd */ \"(app-pages-browser)/./node_modules/.pnpm/antd@5.26.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Spin_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Spin,message!=!antd */ \"(app-pages-browser)/./node_modules/.pnpm/antd@5.26.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Spin_message_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Spin,message!=!antd */ \"(app-pages-browser)/./node_modules/.pnpm/antd@5.26.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Spin_message_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Spin,message!=!antd */ \"(app-pages-browser)/./node_modules/.pnpm/antd@5.26.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/antd/es/spin/index.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/.pnpm/framer-motion@11.18.2_react_cce3d889a93a74b971da0f2788960b34/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/.pnpm/framer-motion@11.18.2_react_cce3d889a93a74b971da0f2788960b34/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ImagePreview__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ImagePreview */ \"(app-pages-browser)/./src/components/ImagePreview.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ConversationPage() {\n    _s();\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imagePreview, setImagePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedImageData, setUploadedImageData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [analysisResult, setAnalysisResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAnalyzing, setIsAnalyzing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 清理预览URL\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConversationPage.useEffect\": ()=>{\n            return ({\n                \"ConversationPage.useEffect\": ()=>{\n                    if (imagePreview) {\n                        URL.revokeObjectURL(imagePreview);\n                    }\n                }\n            })[\"ConversationPage.useEffect\"];\n        }\n    }[\"ConversationPage.useEffect\"], [\n        imagePreview\n    ]);\n    // 处理文件选择\n    const handleFileSelect = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (!file) return;\n        // 验证文件类型\n        if (!(0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.isValidImageType)(file)) {\n            _barrel_optimize_names_Button_Card_Spin_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error('请选择有效的图片文件 (JPEG, PNG, GIF, WebP)');\n            return;\n        }\n        // 验证文件大小 (10MB)\n        if (file.size > 10 * 1024 * 1024) {\n            _barrel_optimize_names_Button_Card_Spin_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error('图片文件大小不能超过 10MB');\n            return;\n        }\n        setSelectedImage(file);\n        setError(null);\n        setAnalysisResult(null);\n        setUploadedImageData(null);\n        // 清理之前的预览URL\n        if (imagePreview) {\n            URL.revokeObjectURL(imagePreview);\n        }\n        // 创建预览URL\n        const previewUrl = URL.createObjectURL(file);\n        setImagePreview(previewUrl);\n    };\n    // 上传图片\n    const handleImageUpload = async ()=>{\n        if (!selectedImage) {\n            _barrel_optimize_names_Button_Card_Spin_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].warning('请先选择一张图片');\n            return;\n        }\n        setIsUploading(true);\n        setError(null);\n        try {\n            const response = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.uploadImage)(selectedImage);\n            setUploadedImageData(response);\n            _barrel_optimize_names_Button_Card_Spin_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success('图片上传成功！');\n        } catch (error) {\n            console.error('Upload error:', error);\n            setError('图片上传失败，请重试');\n            _barrel_optimize_names_Button_Card_Spin_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error('图片上传失败，请重试');\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    // 分析图片\n    const handleImageAnalysis = async ()=>{\n        if (!uploadedImageData) {\n            _barrel_optimize_names_Button_Card_Spin_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].warning('请先上传图片');\n            return;\n        }\n        setIsAnalyzing(true);\n        setError(null);\n        try {\n            const response = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.analyzeImage)({\n                image_url: uploadedImageData.url,\n                prompt: \"从多个角度描述该图像\"\n            });\n            setAnalysisResult(response);\n            _barrel_optimize_names_Button_Card_Spin_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success('图片分析完成！');\n        } catch (error) {\n            console.error('Analysis error:', error);\n            setError('图片分析失败，请重试');\n            _barrel_optimize_names_Button_Card_Spin_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error('图片分析失败，请重试');\n        } finally{\n            setIsAnalyzing(false);\n        }\n    };\n    // 重置状态\n    const handleReset = ()=>{\n        setSelectedImage(null);\n        setImagePreview(null);\n        setUploadedImageData(null);\n        setAnalysisResult(null);\n        setError(null);\n        if (fileInputRef.current) {\n            fileInputRef.current.value = '';\n        }\n        if (imagePreview) {\n            URL.revokeObjectURL(imagePreview);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-neutral-50 via-white to-primary-50/30 dark:from-neutral-900 dark:via-neutral-800 dark:to-neutral-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent mb-4\",\n                            children: \"Image Analyzer\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-neutral-600 dark:text-neutral-300 max-w-2xl mx-auto\",\n                            children: \"AI-powered image analysis platform - Upload, analyze, and discover insights from your images\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                delay: 0.2\n                            },\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Spin_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"p-6 shadow-gemini border-0 bg-white/80 backdrop-blur-sm dark:bg-neutral-800/80\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold mb-6 flex items-center text-neutral-800 dark:text-neutral-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileImage_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"mr-3 text-primary-500\",\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"图片显示区域\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-2 border-dashed border-neutral-300 dark:border-neutral-600 rounded-xl p-4 text-center bg-neutral-50/50 dark:bg-neutral-700/50 transition-all duration-300 hover:border-primary-400 dark:hover:border-primary-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ImagePreview__WEBPACK_IMPORTED_MODULE_4__.ImagePreview, {\n                                                src: imagePreview,\n                                                alt: \"Selected image\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectedImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 10\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            className: \"mt-6 p-4 bg-neutral-100 dark:bg-neutral-700 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-400 space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"文件名:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 24\n                                                            }, this),\n                                                            \" \",\n                                                            selectedImage.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"文件大小:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 172,\n                                                                columnNumber: 24\n                                                            }, this),\n                                                            \" \",\n                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatFileSize)(selectedImage.size)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Spin_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"p-6 shadow-gemini border-0 bg-white/80 backdrop-blur-sm dark:bg-neutral-800/80\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ref: fileInputRef,\n                                                type: \"file\",\n                                                accept: \"image/*\",\n                                                onChange: handleFileSelect,\n                                                className: \"hidden\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Spin_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                type: \"primary\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileImage_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 25\n                                                }, void 0),\n                                                onClick: ()=>{\n                                                    var _fileInputRef_current;\n                                                    return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                },\n                                                className: \"w-full h-12 text-base font-medium bg-primary-500 hover:bg-primary-600 border-0 shadow-lg hover:shadow-xl transition-all duration-200\",\n                                                size: \"large\",\n                                                children: \"选择图片\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                                                children: [\n                                                    selectedImage && !uploadedImageData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            height: 'auto'\n                                                        },\n                                                        exit: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Spin_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            type: \"default\",\n                                                            loading: isUploading,\n                                                            onClick: handleImageUpload,\n                                                            className: \"w-full h-12 text-base font-medium bg-google-green hover:bg-green-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200\",\n                                                            size: \"large\",\n                                                            children: isUploading ? '上传中...' : '上传图片'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    uploadedImageData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            height: 'auto'\n                                                        },\n                                                        exit: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Spin_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            type: \"default\",\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileImage_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 31\n                                                            }, void 0),\n                                                            loading: isAnalyzing,\n                                                            onClick: handleImageAnalysis,\n                                                            className: \"w-full h-12 text-base font-medium bg-secondary-500 hover:bg-secondary-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200\",\n                                                            size: \"large\",\n                                                            children: isAnalyzing ? '分析中...' : '描述图像'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    (selectedImage || uploadedImageData || analysisResult) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            height: 'auto'\n                                                        },\n                                                        exit: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Spin_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            type: \"text\",\n                                                            onClick: handleReset,\n                                                            className: \"w-full h-10 text-base font-medium text-neutral-600 hover:text-neutral-800 dark:text-neutral-400 dark:hover:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-700 transition-all duration-200\",\n                                                            size: \"large\",\n                                                            children: \"重新开始\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                delay: 0.4\n                            },\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Spin_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"p-6 shadow-gemini border-0 bg-white/80 backdrop-blur-sm dark:bg-neutral-800/80\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold mb-6 flex items-center text-neutral-800 dark:text-neutral-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileImage_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-3 text-secondary-500\",\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"分析结果\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border border-neutral-200 dark:border-neutral-700 rounded-xl p-6 min-h-[300px] max-h-[500px] overflow-y-auto bg-neutral-50/50 dark:bg-neutral-700/50\",\n                                            children: isAnalyzing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                initial: {\n                                                    opacity: 0\n                                                },\n                                                animate: {\n                                                    opacity: 1\n                                                },\n                                                className: \"flex flex-col items-center justify-center h-48\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Spin_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        size: \"large\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-3 text-neutral-600 dark:text-neutral-400 text-lg font-medium mt-4\",\n                                                        children: \"正在分析图片...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-neutral-500 dark:text-neutral-500 text-sm mt-2\",\n                                                        children: \"请稍候，AI正在处理您的图片\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 19\n                                            }, this) : analysisResult ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-neutral-900 dark:text-white mb-3 text-lg\",\n                                                                children: \"检测结果:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-white dark:bg-neutral-800 rounded-lg p-4 border border-neutral-200 dark:border-neutral-600\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                                    className: \"text-neutral-700 dark:text-neutral-300 leading-relaxed text-sm font-mono whitespace-pre-wrap overflow-x-auto\",\n                                                                    children: analysisResult.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 292,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    analysisResult.tags && analysisResult.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-neutral-900 dark:text-white mb-3 text-lg\",\n                                                                children: \"相关标签:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-2\",\n                                                                children: analysisResult.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.span, {\n                                                                        initial: {\n                                                                            opacity: 0,\n                                                                            scale: 0.8\n                                                                        },\n                                                                        animate: {\n                                                                            opacity: 1,\n                                                                            scale: 1\n                                                                        },\n                                                                        transition: {\n                                                                            delay: index * 0.1\n                                                                        },\n                                                                        className: \"px-4 py-2 bg-gradient-to-r from-primary-100 to-secondary-100 dark:from-primary-900/30 dark:to-secondary-900/30 text-primary-800 dark:text-primary-200 rounded-full text-sm font-medium border border-primary-200 dark:border-primary-700\",\n                                                                        children: tag\n                                                                    }, index, false, {\n                                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 303,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center h-48 text-neutral-500 dark:text-neutral-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileImage_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            size: 48,\n                                                            className: \"mx-auto mb-4 opacity-40\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium\",\n                                                            children: \"等待图片分析\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm mt-2 opacity-75\",\n                                                            children: '上传图片并点击\"描述图像\"查看分析结果'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                                    children: error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: -10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            y: -10\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Spin_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"p-4 border-0 bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-red-700 dark:text-red-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileImage_MessageSquare_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        size: 20,\n                                                        className: \"mr-3 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: error\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                                    children: uploadedImageData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: -10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            y: -10\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Spin_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"p-4 border-0 bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-green-700 dark:text-green-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-semibold flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"w-2 h-2 bg-green-500 rounded-full mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"图片上传成功\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm mt-1 opacity-90\",\n                                                        children: [\n                                                            \"文件名: \",\n                                                            uploadedImageData.filename\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 130,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\autocs\\\\ImageIns\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, this);\n}\n_s(ConversationPage, \"SrX5X+XQTLHVQobyMwVbQQsrc2A=\");\n_c = ConversationPage;\nvar _c;\n$RefreshReg$(_c, \"ConversationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});
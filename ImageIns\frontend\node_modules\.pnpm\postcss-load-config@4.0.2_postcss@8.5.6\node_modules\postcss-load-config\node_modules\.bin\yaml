#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/LLM/Learning/ai_coding/autocs/ImageIns/frontend/node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/node_modules:/mnt/d/LLM/Learning/ai_coding/autocs/ImageIns/frontend/node_modules/.pnpm/yaml@2.8.0/node_modules:/mnt/d/LLM/Learning/ai_coding/autocs/ImageIns/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/LLM/Learning/ai_coding/autocs/ImageIns/frontend/node_modules/.pnpm/yaml@2.8.0/node_modules/yaml/node_modules:/mnt/d/LLM/Learning/ai_coding/autocs/ImageIns/frontend/node_modules/.pnpm/yaml@2.8.0/node_modules:/mnt/d/LLM/Learning/ai_coding/autocs/ImageIns/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../yaml@2.8.0/node_modules/yaml/bin.mjs" "$@"
else
  exec node  "$basedir/../../../../../yaml@2.8.0/node_modules/yaml/bin.mjs" "$@"
fi
